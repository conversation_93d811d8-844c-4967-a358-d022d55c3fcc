"""
文档上传日志管理模块
负责记录和管理文档上传的日志信息
"""

import os
import json
import shutil
from datetime import datetime
from typing import Dict, List, Optional, Any
import streamlit as st
from utils.logger import setup_logger

# 设置日志记录器
logger = setup_logger()

class UploadLogManager:
    """文档上传日志管理器"""
    
    def __init__(self):
        self.log_dir = "logs/upload_logs"
        self.ensure_log_dir()
    
    def ensure_log_dir(self):
        """确保日志目录存在"""
        os.makedirs(self.log_dir, exist_ok=True)
    
    def record_upload(
        self, 
        uploaded_file, 
        upload_type: str, 
        content: str = None,
        generation_id: str = None
    ) -> str:
        """
        记录文档上传信息
        
        Args:
            uploaded_file: Streamlit上传的文件对象
            upload_type: 上传类型 ("generation" 或 "review")
            content: 文件内容（可选）
            generation_id: 关联的生成记录ID（可选）
            
        Returns:
            str: 上传记录的ID
        """
        try:
            # 生成唯一的上传记录ID
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            upload_id = f"{upload_type}_{timestamp}"
            
            # 创建上传记录目录
            upload_record_dir = os.path.join(self.log_dir, upload_id)
            os.makedirs(upload_record_dir, exist_ok=True)
            
            # 保存上传的文件
            file_path = None
            if uploaded_file is not None:
                file_path = os.path.join(upload_record_dir, uploaded_file.name)
                with open(file_path, "wb") as f:
                    f.write(uploaded_file.getbuffer())
            
            # 创建上传记录
            upload_record = {
                "upload_id": upload_id,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "upload_type": upload_type,
                "file_info": {
                    "name": uploaded_file.name if uploaded_file else None,
                    "size": uploaded_file.size if uploaded_file else None,
                    "type": uploaded_file.type if uploaded_file else None,
                    "saved_path": file_path
                },
                "content": content,
                "generation_id": generation_id,
                "status": "uploaded"
            }
            
            # 保存记录到JSON文件
            record_path = os.path.join(upload_record_dir, "upload_record.json")
            with open(record_path, "w", encoding="utf-8") as f:
                json.dump(upload_record, f, ensure_ascii=False, indent=2)
            
            logger.info(f"文档上传记录已保存: {upload_id}")
            return upload_id
            
        except Exception as e:
            logger.error(f"记录文档上传失败: {str(e)}")
            raise
    
    def link_to_generation(self, upload_id: str, generation_id: str):
        """
        将上传记录关联到生成记录
        
        Args:
            upload_id: 上传记录ID
            generation_id: 生成记录ID
        """
        try:
            record_path = os.path.join(self.log_dir, upload_id, "upload_record.json")
            if os.path.exists(record_path):
                with open(record_path, "r", encoding="utf-8") as f:
                    record = json.load(f)
                
                record["generation_id"] = generation_id
                record["status"] = "linked"
                record["linked_timestamp"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                
                with open(record_path, "w", encoding="utf-8") as f:
                    json.dump(record, f, ensure_ascii=False, indent=2)
                
                logger.info(f"上传记录 {upload_id} 已关联到生成记录 {generation_id}")
            
        except Exception as e:
            logger.error(f"关联上传记录失败: {str(e)}")
    
    def get_upload_logs(self) -> List[Dict[str, Any]]:
        """
        获取所有上传日志记录
        
        Returns:
            List[Dict]: 上传日志记录列表
        """
        try:
            logs = []
            if not os.path.exists(self.log_dir):
                return logs
            
            for upload_dir in os.listdir(self.log_dir):
                record_path = os.path.join(self.log_dir, upload_dir, "upload_record.json")
                if os.path.exists(record_path):
                    try:
                        with open(record_path, "r", encoding="utf-8") as f:
                            record = json.load(f)
                        logs.append(record)
                    except Exception as e:
                        logger.warning(f"读取上传记录失败: {record_path}, 错误: {str(e)}")
            
            # 按时间戳倒序排列
            logs.sort(key=lambda x: x.get("timestamp", ""), reverse=True)
            return logs
            
        except Exception as e:
            logger.error(f"获取上传日志失败: {str(e)}")
            return []
    
    def get_upload_file_path(self, upload_id: str) -> Optional[str]:
        """
        获取上传文件的路径

        Args:
            upload_id: 上传记录ID

        Returns:
            Optional[str]: 文件路径，如果不存在返回None
        """
        try:
            record_path = os.path.join(self.log_dir, upload_id, "upload_record.json")
            if os.path.exists(record_path):
                with open(record_path, "r", encoding="utf-8") as f:
                    record = json.load(f)
                # 直接返回文件在上传目录中的路径
                file_name = record.get("file_info", {}).get("name")
                if file_name:
                    return os.path.join(self.log_dir, upload_id, file_name)
        except Exception as e:
            logger.error(f"获取上传文件路径失败: {str(e)}")
        return None
    
    def find_generation_record(self, generation_id: str) -> Optional[Dict[str, Any]]:
        """
        查找生成记录

        Args:
            generation_id: 生成记录ID

        Returns:
            Optional[Dict]: 生成记录数据，如果不存在返回None
        """
        try:
            # 在generations和reviews目录中查找
            for record_type in ["generations", "reviews"]:
                record_dir = os.path.join("logs", record_type, generation_id)
                result_path = os.path.join(record_dir, "result.json")

                if os.path.exists(result_path):
                    with open(result_path, "r", encoding="utf-8") as f:
                        return json.load(f)
        except Exception as e:
            logger.error(f"查找生成记录失败: {str(e)}")
        return None



# 全局实例
upload_log_manager = UploadLogManager()
