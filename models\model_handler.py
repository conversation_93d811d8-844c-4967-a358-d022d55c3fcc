"""模式处理器模块"""

import streamlit as st
from typing import Optional, List, Tuple

from models.generator_handler import Generator<PERSON>andler
from models.reviewer_handler import ReviewerHandler
from models.dual_model_handler import DualModeHandler
from models import model_manager
from ui.test_case_ui_handler import Test<PERSON><PERSON><PERSON>Handler
from ui.input_utils import render_advanced_options
from utils.logger import setup_logger

logger = setup_logger()

class ModeHandler:
    """模式处理器类，作为不同处理模式的协调器"""
    
    def __init__(self):
        self.generator = GeneratorHandler()
        self.reviewer = ReviewerHandler()
        self.dual_mode = DualModeHandler()
        
    async def handle_generator_mode(
        self,
        user_input: str,
        system_message: str,
        model_name: str,
        test_params: dict
    ) -> Tuple[List[str], Optional[str], str]:
        """处理生成器模式"""
        return await self.generator.handle_generation(
            user_input, system_message, model_name, test_params
        )
            
    async def handle_reviewer_mode(
        self, 
        test_cases: str,
        system_message: str,
        model_name: str,
        review_count: int
    ) -> Tuple[List[str], Optional[str]]:
        """处理评审模式"""
        return await self.reviewer.handle_review(
            test_cases, system_message, model_name, review_count
        )
            
    async def handle_dual_mode(
        self,
        user_input: str,
        test_params: dict,
        system_messages: Tuple[str, str],
        roles: Tuple[str, str]
    ):
        """处理双模型模式"""
        return await self.dual_mode.handle_dual_mode(
            user_input, test_params, system_messages, roles
        )

    async def handle_test_case_generation(self, conf: dict) -> None:
        """处理测试用例生成流程"""
        try:
            st.markdown("### 📝 测试用例生成")

            # 获取当前的模型角色
            from models.role_manager import role_manager

            # 检查是否有模型角色设置
            try:
                roles = role_manager.get_roles()
            except ValueError as e:
                # 如果没有设置模型角色，显示提示信息
                st.error("处理测试用例生成失败: 未设置任何模型角色")
                st.info("请先到 **模型设置** 页面配置模型角色。")
                return

            # 判断模式和显示
            is_single_mode = role_manager.is_single_model_mode()

            # 检查当前模式和角色
            if is_single_mode:
                if role_manager.role_type == "reviewer":
                    st.error("当前模式为仅评审模式，不能生成测试用例")
                    return
                elif not role_manager.generator_model:
                    st.error("请先设置生成模型")
                    return

            # 显示当前模式
            from ui.handlers import display_current_mode
            display_current_mode(is_single_mode, role_manager.role_type)

            # 获取用户输入
            from ui.input_utils import get_user_input, get_test_parameters
            user_input = get_user_input()
            if not user_input:
                st.info("请填写需求描述，或上传需求文档以自动填充。")
                return
                
            # 保存用户输入到会话状态
            st.session_state.user_input = user_input

            # 状态管理初始化
            if "generation_state" not in st.session_state:
                st.session_state.generation_state = "ready"

            # 获取参数和提示词
            test_params = get_test_parameters()
            from utils.message_utils import load_system_messages
            system_messages = load_system_messages()
            
            # 显示操作按钮
            show_btn = is_single_mode or (getattr(role_manager, "generator_model", None) and getattr(role_manager, "reviewer_model", None))
            
            if st.session_state.generation_state == "running":
                await TestCaseUIHandler.handle_generation_ui(
                    self, conf, is_single_mode, roles, system_messages, test_params
                )
            elif st.session_state.generation_state != "completed" and show_btn:
                confirm_btn = st.button(
                    "确认生成并评审测试用例" if not is_single_mode else "确认生成测试用例",
                    key="confirm_generate",
                    type="primary"
                )
                if confirm_btn:
                    st.session_state.generation_state = "running"
                    st.rerun()
                    
        except Exception as e:
            logger.error(f"处理测试用例生成失败: {str(e)}")
            st.error(f"处理测试用例生成失败: {str(e)}")
            st.session_state.generation_state = "ready"

    async def handle_test_case_review(self) -> None:
        """处理测试用例评审流程"""
        try:
            # 检查是否为评审模式
            from models.role_manager import role_manager
            if not role_manager.role_type == "reviewer" or not role_manager.is_single_model_mode():
                st.error("当前不是评审模式，请在模型设置中设置为仅评审模式")
                return
                
            # 验证评审模型设置
            reviewer_model = role_manager.reviewer_model
            if not reviewer_model:
                st.error("评审模型未设置，请在模型设置中选择评审模型")
                return
                
            # 显示当前模式
            from ui.handlers import display_current_mode
            display_current_mode(True, "reviewer")
            
            # 处理文件上传
            uploaded_file = st.file_uploader(
                "上传测试用例Excel文件",
                type=["xlsx", "xls"],
                help="支持.xlsx或.xls格式的测试用例文件",
                key="review_file_uploader"
            )

            if uploaded_file is None:
                return

            # 记录文档上传日志（每次上传只记录一次）
            try:
                from utils.excel_utils import load_excel_testcases
                test_cases_content = load_excel_testcases(uploaded_file)

                # 使用文件的file_id作为唯一标识
                file_id = uploaded_file.file_id if hasattr(uploaded_file, 'file_id') else f"{uploaded_file.name}_{uploaded_file.size}"

                # 检查是否已经记录过这次上传
                if st.session_state.get("last_logged_review_file_id") != file_id:
                    from utils.upload_log_manager import upload_log_manager
                    upload_id = upload_log_manager.record_upload(
                        uploaded_file=uploaded_file,
                        upload_type="review",
                        content=test_cases_content
                    )
                    # 将upload_id保存到session_state中，用于后续关联
                    st.session_state.current_upload_id = upload_id
                    # 记录这次上传的文件ID
                    st.session_state.last_logged_review_file_id = file_id
            except Exception as e:
                st.warning(f"记录上传日志失败: {str(e)}")
            
            st.info("📋 仅评审模式")
                
            # 状态管理
            if "review_state" not in st.session_state:
                st.session_state.review_state = "ready"
                
            # 显示测试用例预览
            from utils.excel_utils import load_excel_testcases
            test_cases_str = load_excel_testcases(uploaded_file)
            st.markdown("### 测试用例预览")
            st.markdown(test_cases_str)
            st.divider()
            
            # 获取评审参数
            col1, col2 = st.columns([3, 1])
            with col1:
                review_count = st.number_input("评审轮次", min_value=1, value=1)
            
            # 状态控制和UI显示
            if st.session_state.review_state == "completed":
                st.success("✅ 评审已完成")
                if st.button("重新评审", key="restart_review", type="primary"):
                    st.session_state.review_state = "ready"
                    st.rerun()
            elif st.session_state.review_state == "running":
                await TestCaseUIHandler.handle_review_ui(
                    self, reviewer_model, test_cases_str, review_count
                )
            else:  # ready
                if st.button("确认开始评审", key="confirm_review_excel", type="primary"):
                    st.session_state.review_state = "running"
                    st.rerun()
                    
        except Exception as e:
            logger.error(f"处理测试用例评审失败: {str(e)}")
            st.error(f"处理测试用例评审失败: {str(e)}")
            
    async def cleanup(self):
        """清理资源，确保所有模型和会话正确关闭"""
        try:
            await model_manager.close()
        except Exception as e:
            logger.error(f"清理资源时出错: {str(e)}")
            
    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.cleanup()
            
model_handler = ModeHandler()
