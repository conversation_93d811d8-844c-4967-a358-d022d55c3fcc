#!/usr/bin/env python3
"""
测试UI组件功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import streamlit as st
from utils.auth_manager import auth_manager
from ui.auth_components import render_user_management, render_user_profile

# 模拟session state
class MockSessionState:
    def __init__(self):
        self.data = {
            'authentication_status': True,
            'username': 'admin',
            'user_info': {
                'username': 'admin',
                'name': '系统管理员',
                'email': '<EMAIL>',
                'role': 'admin'
            }
        }
    
    def get(self, key, default=None):
        return self.data.get(key, default)
    
    def __setattr__(self, key, value):
        if key == 'data':
            super().__setattr__(key, value)
        else:
            self.data[key] = value
    
    def __getattr__(self, key):
        return self.data.get(key)

def test_ui_components():
    """测试UI组件"""
    print("=== 测试UI组件功能 ===")
    
    try:
        # 设置模拟的session state
        st.session_state = MockSessionState()
        
        print("✅ Session state 设置完成")
        print(f"   - 认证状态: {st.session_state.authentication_status}")
        print(f"   - 用户名: {st.session_state.username}")
        print(f"   - 用户信息: {st.session_state.user_info}")
        
        # 测试获取当前用户
        current_user = auth_manager.get_current_user()
        print(f"✅ 获取当前用户: {current_user}")
        
        # 测试管理员检查
        is_admin = auth_manager.is_admin()
        print(f"✅ 管理员检查: {is_admin}")
        
        # 测试获取所有用户
        all_users = auth_manager.get_all_users()
        print(f"✅ 获取所有用户: {len(all_users)} 个用户")
        
        print("\n🎉 UI组件测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_ui_components()
