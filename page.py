"""
主程序模块

"""

import streamlit as st

# 必须首先设置页面配置
st.set_page_config(
    page_title="测试用例生成工具",
    page_icon="✨",
    layout="wide",
    initial_sidebar_state="expanded"
)

from utils.config_manager import ConfigManager
from utils.logger import setup_logger
from utils.async_utils import async_to_sync, safe_async_call
from utils.startup_check import startup_checker
from utils.auth_manager import get_global_auth_manager
from ui.layout import render_header, render_sidebar_menu, render_error_message
from ui.handlers import handle_model_settings, handle_prompt_settings
from ui.session_utils import initialize_session_state
from ui.auth_components import render_login_page, render_user_management, render_user_profile, render_auth_sidebar
from models import model_manager
from models.model_config import model_config
from models.model_handler import ModeHandler
from utils.history_utils import handle_history_data


logger = setup_logger()

def cleanup_resources():
    """清理资源的同步函数"""
    try:
        if st.session_state.get('model_initialized', False):
            # 使用安全的异步调用
            safe_async_call(model_manager.close(), default=None)
        logger.info("资源清理完成")
    except Exception as e:
        logger.error(f"清理资源时出错: {str(e)}")

@async_to_sync
async def handle_model_settings_async(conf):
    """异步处理模型设置"""
    return await handle_model_settings(conf)

@async_to_sync
async def handle_prompt_settings_async():
    """异步处理提示词设置"""
    return await handle_prompt_settings()

@async_to_sync
async def handle_test_case_generation_async(handler, conf):
    """异步处理测试用例生成"""
    return await handler.handle_test_case_generation(conf)

@async_to_sync
async def handle_test_case_review_async(handler):
    """异步处理测试用例评审"""
    return await handler.handle_test_case_review()

@async_to_sync
async def handle_history_data_async():
    """异步处理历史数据"""
    return await handle_history_data()

def main():
    """主函数 - 现在是同步的"""
    try:
        initialize_session_state()

        # 获取auth_manager实例
        auth_manager = get_global_auth_manager()

        # 认证检查 - 如果未登录，显示登录页面
        # cookie恢复逻辑已经在auth_manager.is_authenticated()中处理
        if not auth_manager.is_authenticated():
            # 清除可能的错误状态
            if 'login_error' in st.session_state:
                del st.session_state['login_error']

            result = render_login_page()

            # 检查返回值是否为None或者不是元组
            if result is None:
                st.stop()  # 停止执行，只显示登录页面

            # 如果返回的是元组，解包
            if isinstance(result, tuple) and len(result) == 3:
                _, authentication_status, _ = result
                # 如果登录成功，重新运行页面
                if authentication_status:
                    st.rerun()

            st.stop()  # 停止执行，只显示登录页面

        # 用户已登录，继续正常流程
        if not hasattr(st.session_state, 'config_manager'):
            st.session_state.config_manager = ConfigManager()

        model_config.initialize(st.session_state.config_manager)
        render_header()

        # 运行基本的启动检查（只检查是否有任何可用的API密钥，不显示警告）
        has_any_key, _ = startup_checker.check_api_keys()
        st.session_state.can_continue = has_any_key

        if not has_any_key:
            # 只有完全没有API密钥时才显示配置指南并停止
            startup_checker.show_api_key_setup_guide()
            st.stop()

        # 创建模型处理器
        if 'model_handler' not in st.session_state:
            st.session_state.model_handler = ModeHandler()

        model_handler = st.session_state.model_handler

        # 渲染侧边栏（包含认证信息）
        render_auth_sidebar()
        menu = render_sidebar_menu()
        conf = st.session_state.config_manager.config

        # 根据菜单选择调用相应功能
        if menu == "模型设置":
            handle_model_settings_async(conf)
        elif menu == "提示词设置":
            handle_prompt_settings_async()
        elif menu == "测试用例生成":
            handle_test_case_generation_async(model_handler, conf)
        elif menu == "测试用例评审":
            handle_test_case_review_async(model_handler)
        elif menu == "历史生成数据":
            handle_history_data_async()
        elif menu == "用户管理":
            render_user_management()
        elif menu == "个人设置":
            render_user_profile()

    except Exception as e:
        logger.error(f"程序运行错误: {str(e)}")
        render_error_message(f"程序运行错误: {str(e)}")
    finally:
        # 注册清理函数
        if not st.session_state.get('cleanup_registered', False):
            import atexit
            atexit.register(cleanup_resources)
            st.session_state.cleanup_registered = True

if __name__ == "__main__":
    main()