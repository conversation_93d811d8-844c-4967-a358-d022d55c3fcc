#!/usr/bin/env python3
"""
测试登录状态在页面刷新后是否保持
"""

import requests
import time
import re

def test_session_persistence():
    """测试会话持久性"""
    print("=== 测试登录状态持久性 ===")
    
    session = requests.Session()
    
    try:
        print("1. 首次访问应用...")
        response = session.get("http://localhost:8520", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ 页面请求失败: {response.status_code}")
            return
        
        # 检查是否显示登录页面还是主应用
        html_content = response.text
        
        # 查找登录相关元素
        has_login_form = bool(re.search(r'<input[^>]*type=["\']text["\'][^>]*>', html_content, re.IGNORECASE))
        has_password_input = bool(re.search(r'<input[^>]*type=["\']password["\'][^>]*>', html_content, re.IGNORECASE))
        has_logout_button = bool(re.search(r'登出', html_content))
        
        if has_login_form and has_password_input and not has_logout_button:
            print("   📝 显示登录页面（用户未登录）")
            login_needed = True
        elif has_logout_button and not (has_login_form and has_password_input):
            print("   ✅ 显示主应用（用户已登录）")
            login_needed = False
        else:
            print("   ❓ 页面状态不明确")
            print(f"      登录表单: {has_login_form}")
            print(f"      密码输入: {has_password_input}")
            print(f"      登出按钮: {has_logout_button}")
            login_needed = True
        
        if login_needed:
            print("   需要登录，测试无法继续（请先手动登录）")
            return
        
        print("2. 模拟页面刷新...")
        time.sleep(1)
        
        # 再次请求同一页面（模拟刷新）
        response2 = session.get("http://localhost:8520", timeout=10)
        print(f"   刷新后状态码: {response2.status_code}")
        
        if response2.status_code != 200:
            print(f"   ❌ 刷新后页面请求失败: {response2.status_code}")
            return
        
        # 检查刷新后的状态
        html_content2 = response2.text
        
        has_login_form2 = bool(re.search(r'<input[^>]*type=["\']text["\'][^>]*>', html_content2, re.IGNORECASE))
        has_password_input2 = bool(re.search(r'<input[^>]*type=["\']password["\'][^>]*>', html_content2, re.IGNORECASE))
        has_logout_button2 = bool(re.search(r'登出', html_content2))
        
        print("3. 检查刷新后的状态...")
        
        if has_logout_button2 and not (has_login_form2 and has_password_input2):
            print("   ✅ 刷新后仍然显示主应用（登录状态保持）")
            print("   🎉 会话持久性测试通过！")
        elif has_login_form2 and has_password_input2 and not has_logout_button2:
            print("   ❌ 刷新后显示登录页面（登录状态丢失）")
            print("   💥 会话持久性测试失败！")
        else:
            print("   ❓ 刷新后页面状态不明确")
            print(f"      登录表单: {has_login_form2}")
            print(f"      密码输入: {has_password_input2}")
            print(f"      登出按钮: {has_logout_button2}")
        
        # 检查cookie
        cookies = session.cookies
        if cookies:
            print(f"\n📋 会话Cookie信息:")
            for cookie in cookies:
                print(f"   {cookie.name}: {cookie.value[:20]}...")
        else:
            print("\n⚠️  未发现任何Cookie")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_session_persistence()
