"""历史数据处理工具模块"""

import streamlit as st
import os
import json
from utils.logger import setup_logger

# 设置日志记录器
logger = setup_logger()

async def handle_history_data() -> None:
    """处理历史数据查看"""
    st.markdown("### 📜 历史数据管理")


    # 选择数据类型
    data_type_options = ["测试用例生成记录", "测试用例评审记录", "日志记录"]

    data_type = st.radio(
        "选择数据类型",
        data_type_options,
        index=0,
        horizontal=True
    )

    # 如果选择日志记录，显示上传日志
    if data_type == "日志记录":
        await handle_upload_logs()
        return

    # 根据类型确定数据目录
    base_dir = os.path.join(os.path.dirname(os.path.dirname(__file__)), "logs")
    if data_type == "测试用例生成记录":
        data_dir = os.path.join(base_dir, "generations")
        logger.info(f"使用生成记录目录: {data_dir}")
    else:
        data_dir = os.path.join(base_dir, "reviews")
        logger.info(f"使用评审记录目录: {data_dir}")

    # 确保目录存在
    os.makedirs(data_dir, exist_ok=True)

    # 获取并显示历史数据
    try:
        display_history_records(data_dir, data_type)
    except Exception as e:
        logger.error(f"显示历史数据失败: {str(e)}")
        st.error(f"显示历史数据失败: {str(e)}")

def display_history_records(data_dir: str, data_type: str) -> None:
    """显示历史记录"""
    try:
        subdirs = [d for d in os.listdir(data_dir) if os.path.isdir(os.path.join(data_dir, d))]

        if not subdirs:
            st.info(f"暂无{data_type}")
            return

        # 日期筛选
        date_options = sorted(list(set([d[:8] for d in subdirs if len(d) >= 8])), reverse=True)

        date_selected = st.selectbox("按日期筛选：", ["全部"] + date_options, index=0)

        # 过滤子目录
        if date_selected != "全部":
            subdirs = [d for d in subdirs if d.startswith(date_selected)]
            
        # 获取结果文件
        result_dirs = []
        for sub in sorted(subdirs, reverse=True):
            result_path = os.path.join(data_dir, sub, "result.json")
            if os.path.exists(result_path):
                result_dirs.append((sub, result_path))
                
        if not result_dirs:
            st.info(f"未找到{data_type}")
            return
            
        # 显示结果列表
        record_options = [d[0] for d in result_dirs]

        # 记录调试信息到日志，但不在UI显示
        logger.info(f"记录查找过程 - 数据目录: {data_dir}, 子目录数: {len(subdirs)}, 有效记录数: {len(record_options)}")

        # 仅在开发模式下显示调试信息
        debug_mode = os.environ.get("DEBUG_MODE", "").lower() == "true"
        if debug_mode:
            with st.expander("🔍 调试信息 - 记录查找过程"):
                st.write(f"- 数据目录: {data_dir}")
                st.write(f"- 找到的子目录数: {len(subdirs)}")
                st.write(f"- 有效记录数: {len(record_options)}")
                if record_options:
                    st.write(f"- 前5条记录: {record_options[:5]}")
        # 添加记录数量提示
        st.caption(f"共找到 {len(record_options)} 条记录")
        selected = st.selectbox("选择记录：", record_options, index=0)
        
        if selected:
            result_path = dict(result_dirs)[selected]
            try:
                with open(result_path, "r", encoding="utf-8") as f:
                    data = json.load(f)

                # 兼容两种结构：有 result 字段和无 result 字段
                if "result" in data and isinstance(data["result"], dict):
                    data = data["result"]

                metadata = data.get("metadata", {})
                model_info = data.get("model_info", {})
                responses = data.get("responses", {})

                # 显示结果标题
                st.success(f"已加载数据: {selected}")
                
                # 显示基本信息
                with st.container():
                    st.markdown("### 基本信息")
                    col1, col2 = st.columns(2)
                    with col1:
                        st.info("� 时间: " + metadata.get("timestamp", ""))
                        st.info("� 类型: " + metadata.get("type", ""))
                        st.info("� 模式: " + metadata.get("mode", ""))
                    with col2:
                        if model_info.get("generator"):
                            st.info("🤖 生成模型: " + model_info["generator"])
                        if model_info.get("reviewer"):
                            st.info("🔍 评审模型: " + model_info["reviewer"])

                # 显示具体内容
                with st.expander("查看详细内容", expanded=True):
                    has_content = False
                    if responses.get("generator"):
                        model_name = model_info.get("generator", "原模型")
                        st.markdown(f"### ✨ {model_name}生成的测试用例")
                        from ui.components import render_history_generator_result
                        render_history_generator_result([responses["generator"]], model_name)
                        has_content = True
                    if responses.get("reviewer"):
                        model_name = model_info.get("reviewer", "原模型")
                        st.markdown(f"### 📝 {model_name}的评审结果")
                        from ui.components import render_reviewer_result
                        render_reviewer_result(responses["reviewer"], model_name=model_name)
                        has_content = True
                    if not has_content:
                        st.info("暂无数据")

                st.divider()

                # 下载功能
                file_type = metadata.get("type", selected)
                if st.download_button(
                    "下载JSON数据",
                    json.dumps(data, ensure_ascii=False, indent=2),
                    file_name=f"{file_type}_{selected}.json",
                    mime="application/json"
                ):
                    st.success("下载成功！")
                    
                # 如果有其他文件，也提供下载
                files_dir = os.path.dirname(result_path)
                for file in os.listdir(files_dir):
                    if file != "result.json":
                        with open(os.path.join(files_dir, file), "rb") as f:
                            if st.download_button(
                                f"下载{file}",
                                f.read(),
                                file_name=file,
                                mime="application/octet-stream",
                                key=f"download_{file}"
                            ):
                                st.success("下载成功！")
                            
            except ValueError as e:
                msg = f"数据格式错误: {str(e)}\n文件路径: {result_path}"
                logger.error(msg)
                st.error(msg)
            except json.JSONDecodeError as e:
                msg = f"JSON解析错误: {str(e)}\n文件路径: {result_path}"
                logger.error(msg)
                st.error(msg)
            except Exception as e:
                msg = f"加载数据失败: {str(e)}\n文件路径: {result_path}"
                logger.error(msg)
                st.error(msg)
                
    except Exception as e:
        logger.error(f"显示历史数据失败: {str(e)}")
        st.error(f"显示历史数据失败: {str(e)}")

def get_formatted_records(data_dir: str) -> list:
    """获取格式化的历史记录"""
    result_dirs = []
    for sub in os.listdir(data_dir):
        result_path = os.path.join(data_dir, sub, "result.json")
        if os.path.exists(result_path):
            result_dirs.append((sub, result_path))
    return sorted(result_dirs, reverse=True)

async def handle_upload_logs() -> None:
    """处理上传日志查看"""
    try:
        from utils.upload_log_manager import upload_log_manager

        st.markdown("#### 📁 文档上传日志")

        # 获取所有上传日志
        upload_logs = upload_log_manager.get_upload_logs()

        if not upload_logs:
            st.info("暂无文档上传记录")
            return

        # 日期筛选
        dates = sorted(list(set([log["timestamp"][:10] for log in upload_logs])), reverse=True)
        selected_date = st.selectbox("按日期筛选：", ["全部"] + dates)

        # 过滤日志
        filtered_logs = upload_logs
        if selected_date != "全部":
            filtered_logs = [log for log in upload_logs if log["timestamp"].startswith(selected_date)]

        # 显示日志列表
        for log in filtered_logs:
            file_info = log.get('file_info', {})
            upload_type_display = "测试用例生成" if log.get('upload_type') == 'generation' else "测试用例评审"

            # 使用expander创建可展开的详情
            with st.expander(f"📄 {file_info.get('name', '未知文件')} - {log['timestamp']} - {upload_type_display}"):
                # 基本信息
                st.markdown("**� 基本信息**")
                st.write(f"**上传时间：** {log['timestamp']}")
                st.write(f"**文件名：** {file_info.get('name', '未知')}")
                st.write(f"**文件大小：** {file_info.get('size', 0)} 字节")
                st.write(f"**上传类型：** {upload_type_display}")

                st.divider()

                # 下载原始文件按钮
                try:
                    # 使用upload_log_manager的方法获取文件路径
                    file_path = upload_log_manager.get_upload_file_path(log['upload_id'])
                    file_name = file_info.get('name', 'unknown_file')

                    if file_path and os.path.exists(file_path):
                        with open(file_path, "rb") as f:
                            st.download_button(
                                label="📥 下载原始文件",
                                data=f.read(),
                                file_name=file_name,
                                mime="application/octet-stream",
                                key=f"download_{log['upload_id']}"
                            )
                    else:
                        # 备用方法：直接从目录查找文件
                        upload_dir = os.path.join("logs", "upload_logs", log['upload_id'])
                        if os.path.exists(upload_dir):
                            # 列出目录中的所有文件，排除JSON文件
                            files = [f for f in os.listdir(upload_dir) if f != "upload_record.json"]
                            if files:
                                actual_file_path = os.path.join(upload_dir, files[0])
                                with open(actual_file_path, "rb") as f:
                                    st.download_button(
                                        label="📥 下载原始文件",
                                        data=f.read(),
                                        file_name=files[0],
                                        mime="application/octet-stream",
                                        key=f"download_{log['upload_id']}"
                                    )
                            else:
                                st.error("❌ 原始文件不存在")
                        else:
                            st.error("❌ 上传目录不存在")
                except Exception as e:
                    st.error(f"❌ 下载文件失败: {str(e)}")

                # 内容预览区域
                st.markdown("**� 内容预览**")
                content = log.get('content', '')
                if content:
                    # 限制预览长度
                    preview_content = content[:1000] + "..." if len(content) > 1000 else content
                    st.text_area(
                        "文件内容",
                        value=preview_content,
                        height=150,
                        key=f"content_{log['upload_id']}",
                        disabled=True
                    )
                    if len(content) > 1000:
                        st.info(f"� 内容已截取前1000字符，完整内容共{len(content)}字符")
                else:
                    st.info("📝 无内容预览")

    except Exception as e:
        logger.error(f"处理上传日志失败: {str(e)}")
        st.error(f"处理上传日志失败: {str(e)}")