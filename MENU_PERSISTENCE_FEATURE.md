# 菜单状态保持功能

## 🎯 功能描述

实现了页面刷新后保持当前选中菜单项的功能，提升用户体验。

## ✨ 功能特点

### 1. **状态保持**
- 页面刷新后不会跳回第一个菜单项
- 保持用户最后选择的菜单页面
- 支持所有菜单项：模型设置、提示词设置、测试用例生成、测试用例评审、历史生成数据

### 2. **智能初始化**
- 首次访问时默认显示"模型设置"页面
- 自动处理无效菜单状态（如配置变更导致的菜单项不存在）
- 确保菜单状态的一致性和可靠性

### 3. **无缝集成**
- 不影响原有功能
- 保持所有现有的业务逻辑
- 向后兼容

## 🔧 技术实现

### 修改的文件

#### 1. `ui/layout.py` - 菜单渲染逻辑
```python
def render_sidebar_menu() -> str:
    """
    渲染侧边栏菜单，支持页面刷新后保持当前选中的菜单项
    """
    # 菜单选项列表
    menu_options = [
        "模型设置", "提示词设置", "测试用例生成", 
        "测试用例评审", "历史生成数据"
    ]
    
    # 初始化菜单状态
    if 'current_menu' not in st.session_state:
        st.session_state.current_menu = menu_options[0]
    
    # 获取当前菜单项的索引
    current_index = menu_options.index(st.session_state.current_menu)
    
    # 渲染菜单，使用index参数设置默认选中项
    menu = st.sidebar.radio(
        "功能菜单",
        options=menu_options,
        index=current_index,
        key="sidebar_menu_radio"
    )
    
    # 更新session state
    st.session_state.current_menu = menu
    return menu
```

#### 2. `ui/session_utils.py` - 会话状态初始化
```python
default_states = {
    # ... 其他状态
    'current_menu': '模型设置'  # 默认菜单项
}
```

### 核心机制

1. **Session State 存储**：使用 `st.session_state.current_menu` 存储当前选中的菜单项

2. **索引映射**：将菜单项名称映射为索引，传递给 `st.radio()` 的 `index` 参数

3. **状态同步**：每次菜单选择变化时，同步更新 session state

4. **容错处理**：处理无效菜单状态，确保系统稳定性

## 📋 使用说明

### 用户体验
1. **正常使用**：选择任意菜单项进行操作
2. **页面刷新**：按 F5 或 Ctrl+R 刷新页面
3. **状态保持**：页面重新加载后仍停留在刚才选择的菜单项

### 开发者注意事项
- 菜单项名称变更时需要同步更新 `menu_options` 列表
- 新增菜单项时会自动支持状态保持功能
- Session state 会在浏览器会话期间持续保存

## 🧪 测试验证

### 测试步骤
1. 启动应用程序：`streamlit run page.py`
2. 选择任意非第一个菜单项（如"测试用例生成"）
3. 刷新页面（F5）
4. 验证是否仍停留在"测试用例生成"页面

### 预期结果
- ✅ 刷新后保持在选中的菜单项
- ✅ 所有原有功能正常工作
- ✅ 菜单切换响应正常

## 🔄 兼容性

### 向后兼容
- 完全兼容现有代码
- 不影响任何业务逻辑
- 保持所有API接口不变

### 浏览器支持
- 支持所有现代浏览器
- 依赖 Streamlit 的 session state 机制
- 会话期间状态持久化

## 📈 性能影响

- **内存开销**：极小（仅存储一个字符串）
- **计算开销**：可忽略（简单的字符串比较和索引查找）
- **网络开销**：无额外网络请求

## 🎉 总结

这个功能显著提升了用户体验，特别是在以下场景：
- 用户在特定页面进行复杂操作时需要刷新页面
- 开发调试过程中频繁刷新页面
- 用户希望在特定功能页面保持工作状态

功能实现简洁高效，对系统性能影响微乎其微，是一个实用的用户体验改进。
