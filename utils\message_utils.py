from typing import List, Dict, Optional, Tuple, Any

from utils.logger import setup_logger
from tipwords.prompt_word_template import TESTCASE_WRITER_SYSTEM_MESSAGE_PATH, TESTCASE_READER_SYSTEM_MESSAGE_PATH


# 设置日志（调用自定义logger）
logger = setup_logger()

def load_system_messages() -> Tuple[str, str]:
    """
    加载系统消息模板
    
    Returns:
        Tuple[str, str]: (写入器系统消息, 读取器系统消息)
    """
    try:
        with open(TESTCASE_WRITER_SYSTEM_MESSAGE_PATH, "r", encoding="utf-8") as f:
            system_writer_message = f.read()
            
        with open(TESTCASE_READER_SYSTEM_MESSAGE_PATH, "r", encoding="utf-8") as f:
            system_reader_message = f.read()
            
        return system_writer_message, system_reader_message
    except Exception as e:
        logger.error(f"加载系统消息模板失败: {str(e)}")
        return "", ""