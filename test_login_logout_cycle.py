#!/usr/bin/env python3
"""
测试登录-登出-再登录循环的脚本
用于验证强制登出标志是否正确清除
"""

import streamlit as st
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.auth_manager import get_global_auth_manager
from utils.logger import setup_logger

logger = setup_logger()

def test_login_logout_cycle():
    """测试登录-登出-再登录循环"""
    
    st.title("🔄 登录-登出循环测试")
    
    # 获取auth_manager实例
    auth_manager = get_global_auth_manager()
    
    # 显示当前状态
    st.subheader("当前状态")
    
    # 检查session state中的关键标志
    force_logout = st.session_state.get('force_logout', False)
    logout_requested = st.session_state.get('logout_requested', False)
    authentication_status = st.session_state.get('authentication_status', False)
    username = st.session_state.get('username', '')
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.write("**Session State 标志:**")
        st.write(f"- force_logout: `{force_logout}`")
        st.write(f"- logout_requested: `{logout_requested}`")
        st.write(f"- authentication_status: `{authentication_status}`")
        st.write(f"- username: `{username}`")
    
    with col2:
        st.write("**AuthManager 状态:**")
        is_authenticated = auth_manager.is_authenticated()
        current_user = auth_manager.get_current_user()
        st.write(f"- is_authenticated(): `{is_authenticated}`")
        st.write(f"- current_user: `{current_user}`")
    
    st.divider()
    
    # 如果未认证，显示登录表单
    if not is_authenticated:
        st.subheader("🔐 登录测试")
        
        with st.form("test_login_form"):
            test_username = st.text_input("用户名", value="admin")
            test_password = st.text_input("密码", type="password", value="admin123")
            login_button = st.form_submit_button("测试登录")
            
            if login_button:
                if test_username and test_password:
                    success = auth_manager.verify_and_login(test_username, test_password)
                    if success:
                        st.success("✅ 登录成功！页面将刷新...")
                        st.rerun()
                    else:
                        st.error("❌ 登录失败")
                else:
                    st.warning("⚠️ 请输入用户名和密码")
    
    else:
        # 已认证，显示登出选项
        st.subheader("👋 登出测试")
        st.success(f"✅ 当前已登录用户: {username}")
        
        if st.button("🚪 测试登出", type="primary"):
            auth_manager.logout()
            st.success("✅ 登出成功！页面将刷新...")
            st.rerun()
    
    st.divider()
    
    # 显示详细的session state信息
    with st.expander("🔍 详细 Session State 信息"):
        st.json(dict(st.session_state))
    
    # 手动清除标志的紧急按钮
    st.subheader("🆘 紧急操作")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("清除 force_logout"):
            if 'force_logout' in st.session_state:
                del st.session_state.force_logout
                st.success("已清除 force_logout")
                st.rerun()
    
    with col2:
        if st.button("清除 logout_requested"):
            if 'logout_requested' in st.session_state:
                del st.session_state.logout_requested
                st.success("已清除 logout_requested")
                st.rerun()
    
    with col3:
        if st.button("清除所有认证状态"):
            keys_to_clear = ['force_logout', 'logout_requested', 'authentication_status', 'username', 'name', 'user_info']
            for key in keys_to_clear:
                if key in st.session_state:
                    del st.session_state[key]
            st.success("已清除所有认证状态")
            st.rerun()

if __name__ == "__main__":
    test_login_logout_cycle()
