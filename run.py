#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
启动脚本，负责配置环境并启动Streamlit应用。
"""

import streamlit.web.cli as stcli
import os
import sys
from pathlib import Path
from utils.logger import setup_logger


# 设置日志（调用自定义logger）
logger = setup_logger()

def resolve_path(path: str) -> str:
    """
    解析路径
    
    Args:
        path: 相对路径
        
    Returns:
        str: 绝对路径
    """
    path_dir=os.path.abspath(os.path.join(os.getcwd(), path))
    logger.info(f"解析路径: {path} -> {path_dir}")
    return path_dir


def setup_environment():
    """配置环境变量"""
    # 设置Python路径
    sys.path.append(os.getcwd())
    
    # 设置Streamlit配置
    os.environ["STREAMLIT_BROWSER_GATHER_USAGE_STATS"] = "false"
    os.environ["STREAMLIT_SERVER_RUN_ON_SAVE"] = "true"
    os.environ["STREAMLIT_SERVER_MAX_UPLOAD_SIZE"] = "10"
    
    # 设置日志级别info，debug，error
    # os.environ["STREAMLIT_LOG_LEVEL"] = "error"
    os.environ["STREAMLIT_LOG_LEVEL"] = "info"

    # 打印环境变量
    logger.info(f"STREAMLIT日志级别: STREAMLIT_LOG_LEVEL={os.getenv('STREAMLIT_LOG_LEVEL')}")

    # 容器环境下调整日志路径
    if os.path.exists('/.dockerenv'):
        os.environ["LOG_DIR"] = "/app/logs"
    else:
        os.environ["LOG_DIR"] = "logs"
def main():
    """主函数"""
    try:  
        logger.info("启动应用程序")
        
        # 配置环境
        setup_environment()
        
        # 设置Streamlit参数
        sys.argv = [
            "streamlit",
            "run",
            resolve_path("page.py"),
            "--global.developmentMode=false",
            "--server.maxUploadSize=10",
            "--server.address=localhost",
            "--server.port=8501",
            "--browser.serverAddress=localhost",
            "--theme.base=light",
            "--theme.primaryColor=#FF4B4B",
            "--theme.backgroundColor=#FFFFFF",
            "--theme.secondaryBackgroundColor=#F0F2F6",
            "--theme.textColor=#262730",
            "--theme.font=sans serif"
        ]
        
        # 启动应用
        logger.info("启动Streamlit服务器")
        sys.exit(stcli.main())
        
    except Exception as e:
        logger.error(f"启动失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()