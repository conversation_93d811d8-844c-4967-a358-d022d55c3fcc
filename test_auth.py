"""
测试认证系统的简化版本
"""

import streamlit as st

# 必须首先设置页面配置
st.set_page_config(
    page_title="认证测试",
    page_icon="🔐",
    layout="wide"
)

from utils.auth_manager import auth_manager
from ui.auth_components import render_login_page

def main():
    
    st.title("🔐 认证系统测试")
    
    try:
        # 检查是否已登录
        if not auth_manager.is_authenticated():
            st.info("请登录以继续")
            name, authentication_status, username = render_login_page()
            
            if authentication_status:
                st.success(f"欢迎 {name}!")
                st.rerun()
            elif authentication_status is False:
                st.error("用户名或密码错误")
            else:
                st.stop()
        else:
            # 已登录，显示用户信息
            user_info = auth_manager.get_current_user()
            st.success(f"已登录用户: {user_info['name']}")
            
            if st.button("登出"):
                auth_manager.logout()
                st.rerun()
                
    except Exception as e:
        st.error(f"错误: {str(e)}")
        import traceback
        st.code(traceback.format_exc())

if __name__ == "__main__":
    main()
