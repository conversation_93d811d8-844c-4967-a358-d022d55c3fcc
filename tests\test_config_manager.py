"""
配置管理器测试
"""

import pytest
import tempfile
import os
from pathlib import Path
from unittest.mock import patch, MagicMock

# 添加项目根目录到路径
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.config_manager import ConfigManager
from utils.error_handler import ConfigError


class TestConfigManager:
    """配置管理器测试类"""
    
    def setup_method(self):
        """每个测试方法前的设置"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "test_config.ini"
        
        # 创建测试配置文件
        config_content = """
[deepseek]
choice = True
api_key = test_key_deepseek
base_url = https://api.deepseek.com/
model = deepseek-chat
tokens = 2048
temperature = 0.7
top = 0.9
base_url_list = https://api.deepseek.com/,
model_list = deepseek-chat,

[qwen]
choice = False
api_key = test_key_qwen
base_url = https://dashscope.aliyuncs.com/
model = qwen-max
tokens = 4096
temperature = 0.8
top = 0.95
base_url_list = https://dashscope.aliyuncs.com/,
model_list = qwen-max,
"""
        with open(self.config_file, 'w', encoding='utf-8') as f:
            f.write(config_content)
    
    def teardown_method(self):
        """每个测试方法后的清理"""
        import shutil
        shutil.rmtree(self.temp_dir, ignore_errors=True)
    
    @patch('utils.config_manager.Path')
    def test_config_manager_initialization(self, mock_path):
        """测试配置管理器初始化"""
        # 模拟配置文件路径
        mock_path.return_value.parent.parent = Path(self.temp_dir)
        
        with patch.object(ConfigManager, 'config_path', self.config_file):
            config_manager = ConfigManager()
            assert config_manager.config is not None
            assert len(config_manager.config.sections()) == 2
    
    @patch('utils.config_manager.Path')
    def test_get_model_config_success(self, mock_path):
        """测试成功获取模型配置"""
        mock_path.return_value.parent.parent = Path(self.temp_dir)
        
        with patch.object(ConfigManager, 'config_path', self.config_file):
            config_manager = ConfigManager()
            
            # 测试获取deepseek配置
            deepseek_config = config_manager.get_model_config('deepseek')
            assert deepseek_config is not None
            assert deepseek_config.name == 'deepseek-chat'
            assert deepseek_config.tokens == 2048
            assert deepseek_config.choice is True
    
    @patch('utils.config_manager.Path')
    def test_get_model_config_not_found(self, mock_path):
        """测试获取不存在的模型配置"""
        mock_path.return_value.parent.parent = Path(self.temp_dir)
        
        with patch.object(ConfigManager, 'config_path', self.config_file):
            config_manager = ConfigManager()
            
            # 测试获取不存在的模型
            result = config_manager.get_model_config('nonexistent')
            assert result is None
    
    def test_load_config_file_not_found(self):
        """测试配置文件不存在的情况"""
        non_existent_file = Path(self.temp_dir) / "nonexistent.ini"
        
        with patch.object(ConfigManager, 'config_path', non_existent_file):
            with pytest.raises(ConfigError) as exc_info:
                ConfigManager()
            assert "配置文件不存在" in str(exc_info.value)
    
    @patch('utils.config_manager.env_config')
    @patch('utils.config_manager.Path')
    def test_api_key_from_env(self, mock_path, mock_env_config):
        """测试从环境变量获取API密钥"""
        mock_path.return_value.parent.parent = Path(self.temp_dir)
        mock_env_config.get_api_key.return_value = "env_api_key"
        
        # 创建没有API密钥的配置文件
        config_content = """
[deepseek]
choice = True
api_key = 
base_url = https://api.deepseek.com/
model = deepseek-chat
tokens = 2048
temperature = 0.7
top = 0.9
base_url_list = https://api.deepseek.com/,
model_list = deepseek-chat,
"""
        config_file_no_key = Path(self.temp_dir) / "config_no_key.ini"
        with open(config_file_no_key, 'w', encoding='utf-8') as f:
            f.write(config_content)
        
        with patch.object(ConfigManager, 'config_path', config_file_no_key):
            config_manager = ConfigManager()
            deepseek_config = config_manager.get_model_config('deepseek')
            
            assert deepseek_config.api_key == "env_api_key"
            mock_env_config.get_api_key.assert_called_with('deepseek')


if __name__ == "__main__":
    pytest.main([__file__])
