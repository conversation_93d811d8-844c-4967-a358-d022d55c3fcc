"""双模型模式处理模块"""

from typing import Tuple, List, Optional
import streamlit as st
from dataclasses import dataclass

from models.base_handler import BaseHandler
from models.generator_handler import GeneratorHandler
from models.reviewer_handler import ReviewerHandler
from utils.logger import setup_logger

logger = setup_logger()

@dataclass
class DualModeResult:
    """双模型模式处理结果"""
    cases: List[str]
    reviews: List[str] 
    error: Optional[str]
    generator_model: Optional[str]

class DualModeHandler(BaseHandler):
    """双模型模式处理类"""
    
    def __init__(self):
        super().__init__()
        self.generator = GeneratorHandler()
        self.reviewer = ReviewerHandler()
    
    async def handle_dual_mode(
        self,
        user_input: str,
        test_params: dict,
        system_messages: Tuple[str, str],
        roles: Tuple[str, str]
    ) -> DualModeResult:
        """处理双模型模式
        
        Args:
            user_input: 用户输入的需求描述
            test_params: 测试参数，包含review_count等
            system_messages: (生成器系统消息, 评审器系统消息)
            roles: (生成器角色, 评审器角色)
            
        Returns:
            DualModeResult: 包含生成的测试用例、评审结果、错误信息和使用的生成器模型
        """
        try:
            # 1. 生成测试用例
            cases, gen_error, model_name = await self.generator.handle_generation(
                user_input,
                system_messages[0],
                roles[0],
                test_params
            )
            
            if gen_error:
                return DualModeResult(cases=[], reviews=[], error=gen_error, generator_model=None)
                
            # 2. 评审测试用例    
            review_results, rev_error = await self.reviewer.handle_review(
                "\n".join(cases),
                system_messages[1],
                roles[1],
                test_params.get('review_count', 1)  # 提供默认值
            )
            
            return DualModeResult(
                cases=cases,
                reviews=review_results,
                error=rev_error,
                generator_model=model_name
            )
            
        except Exception as e:
            error_msg = f"双模型模式处理失败: {str(e)}"
            logger.error(error_msg)
            return DualModeResult(cases=[], reviews=[], error=error_msg, generator_model=None)
