"""
用户认证管理模块

提供用户登录、权限验证、用户管理等功能
"""

import streamlit as st
import streamlit.components.v1 as components
import streamlit_authenticator as stauth
import yaml
import os
import bcrypt
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any, Union, Protocol
from utils.logger import setup_logger

logger = setup_logger()

class AuthManagerProtocol(Protocol):
    """认证管理器协议接口"""
    def is_authenticated(self) -> bool: ...
    def get_current_user(self) -> Optional[Dict[str, Any]]: ...
    def is_admin(self) -> bool: ...
    def verify_and_login(self, username: str, password: str) -> bool: ...
    def get_all_users(self) -> List[Dict[str, Any]]: ...
    def add_user(self, username: str, name: str, email: str, password: str, role: str) -> bool: ...
    def update_user_password(self, username: str, new_password: str) -> bool: ...
    def delete_user(self, username: str) -> bool: ...
    def logout(self) -> None: ...

class AuthManager:
    """用户认证管理器"""

    def __init__(self):
        self.config_path = Path("settings/users.yaml")
        self.authenticator: Optional[stauth.Authenticate] = None
        self.config: Optional[Dict[str, Any]] = None

        # 为每个实例生成唯一的ID，用于避免key冲突
        import time
        self.instance_id = str(int(time.time() * 1000000))

        self._ensure_config_exists()
        self._load_config()

        # 延迟cookie恢复，只在需要时进行
        self._cookie_restored = False
        
        # 在初始化时立即尝试恢复认证状态
        try:
            if hasattr(st, 'session_state'):
                self._restore_auth_from_cookie()
                self._cookie_restored = True
        except Exception as e:
            logger.debug(f"初始化时恢复认证状态失败: {e}")

    def _init_session_state(self):
        """初始化session state中的必要键"""
        try:
            # 检查是否在Streamlit上下文中
            if hasattr(st, 'session_state'):
                # 只初始化不存在的键，不覆盖已有的值
                # 这样可以保护已经从cookie恢复的认证状态
                required_keys = {
                    'authentication_status': None,
                    'name': None,
                    'username': None,
                    'logout': False
                }

                for key, default_value in required_keys.items():
                    if key not in st.session_state:
                        logger.info(f"初始化session state键: {key} = {default_value}")
                        st.session_state[key] = default_value
                    else:
                        logger.info(f"session state键 {key} 已存在，值为: {st.session_state[key]}")
        except Exception as e:
            # 如果不在Streamlit上下文中，忽略错误
            logger.debug(f"无法初始化session state: {e}")
    
    def _ensure_config_exists(self):
        """确保用户配置文件存在"""
        if not self.config_path.exists():
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认的用户配置文件"""
        # 创建默认管理员账号 - 使用新版本API
        import bcrypt
        password = 'admin123'
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        default_config = {
            'credentials': {
                'usernames': {
                    'admin': {
                        'email': '<EMAIL>',
                        'name': '系统管理员',
                        'password': hashed_password,  # 默认密码
                        'role': 'admin'
                    }
                }
            },
            'cookie': {
                'name': 'testcase_auth_cookie',
                'key': 'testcase_secret_key_2025',
                'expiry_days': 30
            }
        }
        
        # 确保目录存在
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存配置文件
        with open(self.config_path, 'w', encoding='utf-8') as file:
            yaml.dump(default_config, file, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"已创建默认用户配置文件: {self.config_path}")
        logger.info("默认管理员账号 - 用户名: admin, 密码: admin123")
    
    def _load_config(self):
        """加载用户配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            # 创建认证器 - 使用新版本API
            if config is None:
                raise ValueError("配置文件加载失败")

            self.authenticator = stauth.Authenticate(
                config['credentials'],  # type: ignore
                config['cookie']['name'],  # type: ignore
                config['cookie']['key'],  # type: ignore
                config['cookie']['expiry_days']  # type: ignore
            )
            
            logger.info("用户配置加载成功")
            
        except Exception as e:
            logger.error(f"加载用户配置失败: {e}")
            raise
    
    def login(self) -> Tuple[Optional[str], Optional[bool], Optional[str]]:
        """
        显示登录界面并处理登录逻辑

        Returns:
            Tuple[name, authentication_status, username]: 登录结果
        """
        if not self.authenticator:
            st.error("认证系统未初始化")
            return None, None, None

        # 初始化必要的session state
        if 'authentication_status' not in st.session_state:
            st.session_state.authentication_status = None
        if 'name' not in st.session_state:
            st.session_state.name = None
        if 'username' not in st.session_state:
            st.session_state.username = None
        if 'logout' not in st.session_state:
            st.session_state.logout = False

        # 显示登录组件 - 使用新版本API
        try:
            # 确保登录表单的key是唯一的，避免状态冲突
            login_key = f"login_form_{self.instance_id}"
            result = self.authenticator.login(location='main', key=login_key)

            # 检查返回值
            if result is None or not isinstance(result, tuple) or len(result) != 3:
                # 如果返回None，这通常意味着组件还在初始化或等待用户输入
                # 返回默认值，让页面继续显示登录表单
                return None, None, None

            name, authentication_status, username = result

            if authentication_status == False:
                st.error('用户名或密码错误')
            elif authentication_status == None:
                st.warning('请输入用户名和密码')
            elif authentication_status:
                # 登录成功，保存用户信息到session
                if username:  # 确保username不为None
                    user_info = self._get_user_info(username)
                    st.session_state.user_info = user_info
                    logger.info(f"用户 {username} 登录成功")

            return name, authentication_status, username

        except Exception as e:
            logger.error(f"登录过程中出错: {e}")
            st.error(f"登录系统错误: {e}")
            return None, None, None
    
    def logout(self):
        """登出"""
        logger.info("开始登出流程...")

        # 第一步：设置登出标志，防止任何恢复机制
        st.session_state.logout_requested = True
        st.session_state.force_logout = True
        logger.info("已设置登出标志")

        # 第二步：禁用认证器
        self.authenticator = None
        logger.info("已禁用认证器")

        # 第三步：完全清除session state
        logger.info("开始清除session state...")

        # 获取所有键的列表（避免在迭代时修改字典）
        all_keys = list(st.session_state.keys())

        # 清除所有键
        for key in all_keys:
            try:
                del st.session_state[key]
                logger.debug(f"已删除session state键: {key}")
            except Exception as e:
                logger.warning(f"删除session state键 {key} 时出错: {e}")

        # 第四步：重新设置登出状态（因为上面清除了所有键）
        st.session_state.logout_requested = True
        st.session_state.force_logout = True
        st.session_state.authentication_status = False
        st.session_state.name = None
        st.session_state.username = None
        st.session_state.logout = None
        logger.info("已重新设置登出状态")

        logger.info("用户已登出，已完全重置session state")

        # 第五步：清除浏览器cookie并强制刷新
        try:
            # 使用JavaScript清除所有cookie
            clear_cookies_js = """
            <script>
            // 清除所有cookie
            document.cookie.split(";").forEach(function(c) {
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
            });
            // 清除localStorage和sessionStorage
            localStorage.clear();
            sessionStorage.clear();
            // 强制刷新页面
            setTimeout(function() {
                window.location.reload(true);
            }, 100);
            </script>
            """
            components.html(clear_cookies_js, height=0)
            logger.info("已执行JavaScript清除cookie")
        except Exception as e:
            logger.warning(f"执行JavaScript清除cookie失败: {e}")
            # 备用方案：兼容不同streamlit版本的刷新
            self._rerun()

    def _rerun(self):
        """兼容不同Streamlit版本的刷新页面方法"""
        try:
            rerun_func = getattr(st, "rerun", None)
            if callable(rerun_func):
                rerun_func()
                return
            exp_rerun_func = getattr(st, "experimental_rerun", None)
            if callable(exp_rerun_func):
                exp_rerun_func()
                return
            logger.warning("当前Streamlit版本不支持页面刷新（rerun）")
        except Exception as e:
            logger.error(f"页面刷新失败: {e}")
    
    def verify_and_login(self, username: str, password: str) -> bool:
        """验证用户名密码并登录"""
        try:
            # 加载用户配置
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)

            # 类型安全的配置访问
            if not isinstance(config, dict):
                logger.error("用户配置格式错误：不是有效的字典")
                return False

            credentials = config.get('credentials')
            if not isinstance(credentials, dict):
                logger.error("用户配置格式错误：credentials不存在或格式错误")
                return False

            users = credentials.get('usernames')
            if not isinstance(users, dict):
                logger.error("用户配置格式错误：usernames不存在或格式错误")
                return False

            if username in users:
                user_data = users[username]
                if not isinstance(user_data, dict):
                    logger.error(f"用户 {username} 的配置格式错误")
                    return False

                # 验证密码
                user_password = user_data.get('password')
                if not isinstance(user_password, str):
                    logger.error(f"用户 {username} 的密码配置错误")
                    return False

                if bcrypt.checkpw(password.encode('utf-8'), user_password.encode('utf-8')):
                    # 登录成功，设置session state
                    st.session_state.authentication_status = True
                    st.session_state.username = username
                    st.session_state.name = user_data.get('name', username)
                    st.session_state.user_info = {
                        'username': username,
                        'name': user_data.get('name', username),
                        'email': user_data.get('email', ''),
                        'role': user_data.get('role', 'user')
                    }

                    # 注意：cookie的设置由streamlit-authenticator在后续的login调用中自动处理

                    logger.info(f"用户 {username} 登录成功")
                    return True

            logger.warning(f"用户 {username} 登录失败")
            return False

        except Exception as e:
            logger.error(f"验证登录时出错: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """检查用户是否已认证"""
        # 检查是否有强制登出标志
        if st.session_state.get('force_logout') or st.session_state.get('logout_requested'):
            logger.info("检测到强制登出标志，返回未认证状态")
            return False

        # 每次都尝试从cookie恢复认证状态，确保刷新后能自动恢复
        self._restore_auth_from_cookie()
        self._init_session_state()

        auth_status = st.session_state.get('authentication_status', False)

        # 如果认证状态是从cookie恢复的，优先信任我们的状态
        if st.session_state.get('auth_restored_from_cookie') and st.session_state.get('username'):
            if not auth_status:
                logger.warning("检测到认证状态被重置，从cookie恢复的状态应该保持为True")
                st.session_state.authentication_status = True
                auth_status = True

        logger.info(f"当前认证状态: {auth_status}")

        if not auth_status and st.session_state.get('username'):
            logger.warning(f"认证状态为False，但session_state中有用户名: {st.session_state.get('username')}")
            logger.warning(f"是否从cookie恢复: {st.session_state.get('auth_restored_from_cookie')}")

        return auth_status
    
    def is_admin(self) -> bool:
        """检查当前用户是否为管理员"""
        user_info = st.session_state.get('user_info')
        if not isinstance(user_info, dict):
            return False
        return user_info.get('role') == 'admin'
    
    def get_current_user(self) -> Optional[Dict]:
        """获取当前登录用户信息"""
        user_info = st.session_state.get('user_info')
        logger.debug(f"获取当前用户信息: {user_info}")

        # 如果没有用户信息，尝试从认证状态重新获取
        if not user_info and st.session_state.get('authentication_status') and st.session_state.get('username'):
            username = st.session_state.get('username')
            if username:  # 确保username不为None
                logger.info(f"尝试重新获取用户 {username} 的信息")
                user_info = self._get_user_info(username)
                if user_info:
                    st.session_state.user_info = user_info
                    logger.info(f"成功重新获取用户信息: {username}")

        return user_info
    
    def _get_user_info(self, username: str) -> Dict:
        """获取用户详细信息"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)

            # 类型安全的配置访问
            if not isinstance(config, dict):
                logger.error("用户配置格式错误：不是有效的字典")
                return {}

            credentials = config.get('credentials')
            if not isinstance(credentials, dict):
                logger.error("用户配置格式错误：credentials不存在或格式错误")
                return {}

            usernames = credentials.get('usernames')
            if not isinstance(usernames, dict):
                logger.error("用户配置格式错误：usernames不存在或格式错误")
                return {}

            user_data = usernames.get(username)
            if not isinstance(user_data, dict):
                logger.warning(f"用户 {username} 的配置不存在或格式错误")
                return {}

            return {
                'username': username,
                'name': user_data.get('name', ''),
                'email': user_data.get('email', ''),
                'role': user_data.get('role', 'user')
            }
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return {}
    
    def _restore_auth_from_cookie(self):
        """从cookie恢复认证状态"""
        try:
            if not self.authenticator:
                logger.warning("认证器未初始化，无法从cookie恢复认证状态")
                return

            # 检查是否在Streamlit运行时上下文中
            try:
                # 尝试访问session_state来检查是否在正确的上下文中
                _ = st.session_state
            except Exception:
                logger.debug("不在Streamlit运行时上下文中，跳过cookie认证恢复")
                return

            # 检查是否有登出请求标志
            if st.session_state.get('logout_requested') or st.session_state.get('force_logout'):
                logger.info("检测到登出请求，跳过cookie恢复")
                return

            # 如果已经认证，不需要恢复
            if st.session_state.get('authentication_status'):
                logger.debug("用户已认证，不需要从cookie恢复")
                return

            # 确保session state已初始化，避免login()调用时出错
            self._init_session_state()

            # 尝试从cookie恢复认证状态
            logger.info("尝试从cookie恢复认证状态")

            # 保存当前session state，以防login方法意外修改它
            original_auth_status = st.session_state.get('authentication_status')
            original_name = st.session_state.get('name')
            original_username = st.session_state.get('username')

            # 使用streamlit_authenticator提供的方法检查认证状态
            # streamlit_authenticator会自动处理cookie的验证和恢复
            # 在login方法中会自动检查cookie并恢复认证状态
            result = self.authenticator.login(location='unrendered', key=f'cookie_restore_{self.instance_id}')

            if result and isinstance(result, tuple) and len(result) == 3:
                name, auth_status, username = result
                logger.info(f"Cookie恢复结果: name={name}, auth_status={auth_status}, username={username}")

                if auth_status and isinstance(username, str):
                    logger.info(f"从cookie恢复用户 {username} 的认证状态")

                    # 更新session state - 确保所有必要键都存在
                    st.session_state.authentication_status = True
                    st.session_state.username = username
                    st.session_state.name = name

                    # 确保user_info也被恢复
                    user_info = self._get_user_info(username)
                    if user_info:
                        st.session_state.user_info = user_info
                        logger.info(f"成功从cookie恢复用户信息: {username}")

                    # 设置一个标志，表示认证状态是从cookie恢复的
                    st.session_state.auth_restored_from_cookie = True
                else:
                    logger.info(f"Cookie中没有有效的认证信息: auth_status={auth_status}")
                    # 恢复原始状态，因为login可能已经修改了session state
                    st.session_state.authentication_status = original_auth_status
                    st.session_state.name = original_name
                    st.session_state.username = original_username
            else:
                logger.info(f"Cookie恢复返回无效结果: {result}")
                # 恢复原始状态，因为login可能已经修改了session state
                st.session_state.authentication_status = original_auth_status
                st.session_state.name = original_name
                st.session_state.username = original_username

        except Exception as e:
            logger.error(f"从cookie恢复认证状态失败: {e}")
    
    def get_all_users(self) -> List[Dict]:
        """获取所有用户列表（仅管理员可用）"""
        if not self.is_admin():
            logger.warning("非管理员用户尝试获取用户列表")
            return []

        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)

            if not isinstance(config, dict):
                logger.error("用户配置文件格式错误：不是有效的字典")
                return []

            credentials = config.get('credentials')
            if not isinstance(credentials, dict):
                logger.error("用户配置格式错误：credentials不存在或格式错误")
                return []

            usernames = credentials.get('usernames')
            if not isinstance(usernames, dict):
                logger.error("用户配置格式错误：usernames不存在或格式错误")
                return []

            users = []
            for username, user_data in usernames.items():
                users.append({
                    'username': username,
                    'name': user_data.get('name', ''),
                    'email': user_data.get('email', ''),
                    'role': user_data.get('role', 'user')
                })

            logger.debug(f"成功获取 {len(users)} 个用户信息")
            return users
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return []
    
    def add_user(self, username: str, name: str, email: str, password: str, role: str = 'user') -> bool:
        """添加新用户（仅管理员可用）"""
        if not self.is_admin():
            return False
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            # 使用类型安全的方式访问配置
            if not isinstance(config, dict):
                logger.error("用户配置格式错误：不是有效的字典")
                return False
                
            credentials = config.get('credentials', {})
            if not isinstance(credentials, dict):
                logger.error("用户配置格式错误：credentials不存在或格式错误")
                return False
                
            usernames = credentials.get('usernames', {})
            if not isinstance(usernames, dict):
                logger.error("用户配置格式错误：usernames不存在或格式错误")
                return False
            
            # 检查用户名是否已存在
            if username in usernames:
                return False

            # 添加新用户
            import bcrypt
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # 确保config结构完整
            if 'credentials' not in config:
                config['credentials'] = {}
            if 'usernames' not in config['credentials']:
                config['credentials']['usernames'] = {}
                
            config['credentials']['usernames'][username] = {
                'name': name,
                'email': email,
                'password': hashed_password,
                'role': role
            }
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config, file, default_flow_style=False, allow_unicode=True)
            
            # 重新加载配置
            self._load_config()
            
            logger.info(f"已添加新用户: {username}")
            return True
            
        except Exception as e:
            logger.error(f"添加用户失败: {e}")
            return False
    
    def update_user_password(self, username: str, new_password: str) -> bool:
        """更新用户密码（管理员可更新任何用户，普通用户只能更新自己）"""
        current_user = self.get_current_user()
        if not isinstance(current_user, dict):
            return False
        
        # 权限检查
        if current_user.get('role') != 'admin' and current_user.get('username') != username:
            return False
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            # 使用类型安全的方式访问配置
            if not isinstance(config, dict):
                logger.error("用户配置格式错误：不是有效的字典")
                return False
                
            credentials = config.get('credentials', {})
            if not isinstance(credentials, dict):
                logger.error("用户配置格式错误：credentials不存在或格式错误")
                return False
                
            usernames = credentials.get('usernames', {})
            if not isinstance(usernames, dict):
                logger.error("用户配置格式错误：usernames不存在或格式错误")
                return False
            
            if username not in usernames:
                return False

            # 更新密码
            import bcrypt
            hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            
            # 确保用户数据是字典
            if not isinstance(usernames[username], dict):
                usernames[username] = {}
                
            usernames[username]['password'] = hashed_password
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config, file, default_flow_style=False, allow_unicode=True)
            
            # 重新加载配置
            self._load_config()
            
            logger.info(f"已更新用户 {username} 的密码")
            return True
            
        except Exception as e:
            logger.error(f"更新密码失败: {e}")
            return False
    
    def delete_user(self, username: str) -> bool:
        """删除用户（仅管理员可用，不能删除自己）"""
        if not self.is_admin():
            return False
        
        current_user = self.get_current_user()
        if isinstance(current_user, dict) and current_user.get('username') == username:
            return False  # 不能删除自己
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            # 使用类型安全的方式访问配置
            if not isinstance(config, dict):
                logger.error("用户配置格式错误：不是有效的字典")
                return False
                
            credentials = config.get('credentials', {})
            if not isinstance(credentials, dict):
                logger.error("用户配置格式错误：credentials不存在或格式错误")
                return False
                
            usernames = credentials.get('usernames', {})
            if not isinstance(usernames, dict):
                logger.error("用户配置格式错误：usernames不存在或格式错误")
                return False
            
            if username not in usernames:
                return False

            # 删除用户
            del usernames[username]
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config, file, default_flow_style=False, allow_unicode=True)
            
            # 重新加载配置
            self._load_config()
            
            logger.info(f"已删除用户: {username}")
            return True
            
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return False

# 新增: 单例获取函数
_auth_manager_instance = None

def get_auth_manager() -> AuthManagerProtocol:
    """获取全局唯一的AuthManager实例（单例模式）"""
    global _auth_manager_instance
    if _auth_manager_instance is None:
        try:
            _auth_manager_instance = AuthManager()
        except Exception as e:
            logger.error(f"创建AuthManager实例失败: {e}")
            # 创建备用实例
            class MinimalAuthManager:
                def __init__(self):
                    self.config_path = Path("settings/users.yaml")
                    self.authenticator = None
                    self.config = None

                def is_authenticated(self) -> bool:
                    return False

                def get_current_user(self) -> Optional[Dict[str, Any]]:
                    return None

                def is_admin(self) -> bool:
                    return False

                def verify_and_login(self, username: str, password: str) -> bool:
                    return False

                def get_all_users(self) -> List[Dict[str, Any]]:
                    return []

                def add_user(self, username: str, name: str, email: str, password: str, role: str) -> bool:
                    return False

                def update_user_password(self, username: str, new_password: str) -> bool:
                    return False

                def delete_user(self, username: str) -> bool:
                    return False

                def logout(self) -> None:
                    pass
                    
            _auth_manager_instance = MinimalAuthManager()
    return _auth_manager_instance

# 延迟创建全局实例，避免模块导入时的key冲突
auth_manager = None

def get_global_auth_manager():
    """获取全局auth_manager实例"""
    global auth_manager
    if auth_manager is None:
        try:
            auth_manager = get_auth_manager()
        except Exception as e:
            logger.error(f"初始化auth_manager失败: {e}")
            # 创建一个最小的备用实例
            class MinimalAuthManager:
                def is_authenticated(self) -> bool:
                    return False
                def get_current_user(self) -> Optional[Dict[str, Any]]:
                    return None
                def is_admin(self) -> bool:
                    return False
                def verify_and_login(self, username: str, password: str) -> bool:
                    return False
                def get_all_users(self) -> List[Dict[str, Any]]:
                    return []
                def add_user(self, username: str, name: str, email: str, password: str, role: str) -> bool:
                    return False
                def update_user_password(self, username: str, new_password: str) -> bool:
                    return False
                def delete_user(self, username: str) -> bool:
                    return False
                def logout(self) -> None:
                    pass
            auth_manager = MinimalAuthManager()
    return auth_manager

# 修改导出内容
__all__ = ['AuthManager', 'get_auth_manager', 'get_global_auth_manager']

# 为了向后兼容，提供auth_manager属性访问
def __getattr__(name):
    if name == 'auth_manager':
        return get_global_auth_manager()
    raise AttributeError(f"module '{__name__}' has no attribute '{name}'")
# 修改导出内容
__all__ = ['AuthManager', 'get_auth_manager', 'get_global_auth_manager']

