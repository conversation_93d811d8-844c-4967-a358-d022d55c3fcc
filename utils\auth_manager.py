"""
用户认证管理模块

提供用户登录、权限验证、用户管理等功能
"""

import streamlit as st
import streamlit_authenticator as stauth
import yaml
import os
import bcrypt
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from utils.logger import setup_logger

logger = setup_logger()

class AuthManager:
    """用户认证管理器"""

    def __init__(self):
        self.config_path = Path("settings/users.yaml")
        self.authenticator: Optional[stauth.Authenticate] = None
        self.config: Optional[Dict[str, Any]] = None

        # 初始化必要的session state键
        self._init_session_state()

        self._ensure_config_exists()
        self._load_config()

    def _init_session_state(self):
        """初始化session state中的必要键"""
        try:
            # 检查是否在Streamlit上下文中
            if hasattr(st, 'session_state'):
                required_keys = [
                    'authentication_status',
                    'name',
                    'username',
                    'logout'
                ]

                for key in required_keys:
                    if key not in st.session_state:
                        st.session_state[key] = None
        except Exception as e:
            # 如果不在Streamlit上下文中，忽略错误
            logger.debug(f"无法初始化session state: {e}")
    
    def _ensure_config_exists(self):
        """确保用户配置文件存在"""
        if not self.config_path.exists():
            self._create_default_config()
    
    def _create_default_config(self):
        """创建默认的用户配置文件"""
        # 创建默认管理员账号 - 使用新版本API
        import bcrypt
        password = 'admin123'
        hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')

        default_config = {
            'credentials': {
                'usernames': {
                    'admin': {
                        'email': '<EMAIL>',
                        'name': '系统管理员',
                        'password': hashed_password,  # 默认密码
                        'role': 'admin'
                    }
                }
            },
            'cookie': {
                'name': 'testcase_auth_cookie',
                'key': 'testcase_secret_key_2025',
                'expiry_days': 30
            }
        }
        
        # 确保目录存在
        self.config_path.parent.mkdir(parents=True, exist_ok=True)
        
        # 保存配置文件
        with open(self.config_path, 'w', encoding='utf-8') as file:
            yaml.dump(default_config, file, default_flow_style=False, allow_unicode=True)
        
        logger.info(f"已创建默认用户配置文件: {self.config_path}")
        logger.info("默认管理员账号 - 用户名: admin, 密码: admin123")
    
    def _load_config(self):
        """加载用户配置"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            # 创建认证器 - 使用新版本API
            if config is None:
                raise ValueError("配置文件加载失败")

            self.authenticator = stauth.Authenticate(
                config['credentials'],  # type: ignore
                config['cookie']['name'],  # type: ignore
                config['cookie']['key'],  # type: ignore
                config['cookie']['expiry_days']  # type: ignore
            )
            
            logger.info("用户配置加载成功")
            
        except Exception as e:
            logger.error(f"加载用户配置失败: {e}")
            raise
    
    def login(self) -> Tuple[Optional[str], Optional[bool], Optional[str]]:
        """
        显示登录界面并处理登录逻辑

        Returns:
            Tuple[name, authentication_status, username]: 登录结果
        """
        if not self.authenticator:
            st.error("认证系统未初始化")
            return None, None, None

        # 初始化必要的session state
        if 'authentication_status' not in st.session_state:
            st.session_state.authentication_status = None
        if 'name' not in st.session_state:
            st.session_state.name = None
        if 'username' not in st.session_state:
            st.session_state.username = None
        if 'logout' not in st.session_state:
            st.session_state.logout = False

        # 显示登录组件 - 使用新版本API
        try:
            # 确保登录表单的key是唯一的，避免状态冲突
            login_key = f"login_form_{id(self.authenticator)}"
            result = self.authenticator.login(location='main', key=login_key)

            # 检查返回值
            if result is None or not isinstance(result, tuple) or len(result) != 3:
                # 如果返回None，这通常意味着组件还在初始化或等待用户输入
                # 返回默认值，让页面继续显示登录表单
                return None, None, None

            name, authentication_status, username = result

            if authentication_status == False:
                st.error('用户名或密码错误')
            elif authentication_status == None:
                st.warning('请输入用户名和密码')
            elif authentication_status:
                # 登录成功，保存用户信息到session
                if username:  # 确保username不为None
                    user_info = self._get_user_info(username)
                    st.session_state.user_info = user_info
                    logger.info(f"用户 {username} 登录成功")

            return name, authentication_status, username

        except Exception as e:
            logger.error(f"登录过程中出错: {e}")
            st.error(f"登录系统错误: {e}")
            return None, None, None
    
    def logout(self):
        """登出"""
        try:
            if self.authenticator:
                # 初始化logout状态（如果不存在）
                if 'logout' not in st.session_state:
                    st.session_state.logout = False

                self.authenticator.logout(location='sidebar')
        except Exception as e:
            logger.warning(f"调用authenticator.logout()时出错: {e}")

        # 完全清除session state
        logger.info("开始清除session state...")

        # 获取所有键的列表（避免在迭代时修改字典）
        all_keys = list(st.session_state.keys())

        # 清除所有键
        for key in all_keys:
            try:
                del st.session_state[key]
                logger.debug(f"已删除session state键: {key}")
            except Exception as e:
                logger.warning(f"删除session state键 {key} 时出错: {e}")

        # 强制重新创建认证器
        self.authenticator = None
        try:
            self._load_config()
            logger.info("已重新创建认证器")
        except Exception as e:
            logger.error(f"重新创建认证器失败: {e}")

        logger.info("用户已登出，已完全重置session state")

    def verify_and_login(self, username: str, password: str) -> bool:
        """验证用户名密码并登录"""
        try:
            # 加载用户配置
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)

            if not config or 'credentials' not in config or 'usernames' not in config['credentials']:
                logger.error("用户配置格式错误")
                return False

            users = config['credentials']['usernames']  # type: ignore

            if username in users:
                user_data = users[username]  # type: ignore
                # 验证密码
                if bcrypt.checkpw(password.encode('utf-8'), user_data['password'].encode('utf-8')):  # type: ignore
                    # 登录成功，设置session state
                    st.session_state.authentication_status = True
                    st.session_state.username = username
                    st.session_state.name = user_data['name']  # type: ignore
                    st.session_state.user_info = {
                        'username': username,
                        'name': user_data['name'],  # type: ignore
                        'email': user_data['email'],  # type: ignore
                        'role': user_data.get('role', 'user')  # type: ignore
                    }
                    logger.info(f"用户 {username} 登录成功")
                    return True

            logger.warning(f"用户 {username} 登录失败")
            return False

        except Exception as e:
            logger.error(f"验证登录时出错: {e}")
            return False
    
    def is_authenticated(self) -> bool:
        """检查用户是否已认证"""
        return st.session_state.get('authentication_status', False)
    
    def is_admin(self) -> bool:
        """检查当前用户是否为管理员"""
        user_info = st.session_state.get('user_info', {})
        return user_info.get('role') == 'admin'
    
    def get_current_user(self) -> Optional[Dict]:
        """获取当前登录用户信息"""
        user_info = st.session_state.get('user_info')
        logger.debug(f"获取当前用户信息: {user_info}")

        # 如果没有用户信息，尝试从认证状态重新获取
        if not user_info and st.session_state.get('authentication_status') and st.session_state.get('username'):
            username = st.session_state.get('username')
            if username:  # 确保username不为None
                logger.info(f"尝试重新获取用户 {username} 的信息")
                user_info = self._get_user_info(username)
                if user_info:
                    st.session_state.user_info = user_info
                    logger.info(f"成功重新获取用户信息: {username}")

        return user_info
    
    def _get_user_info(self, username: str) -> Dict:
        """获取用户详细信息"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            user_data = config['credentials']['usernames'].get(username, {})  # type: ignore
            return {
                'username': username,
                'name': user_data.get('name', ''),
                'email': user_data.get('email', ''),
                'role': user_data.get('role', 'user')
            }
        except Exception as e:
            logger.error(f"获取用户信息失败: {e}")
            return {}
    
    def get_all_users(self) -> List[Dict]:
        """获取所有用户列表（仅管理员可用）"""
        if not self.is_admin():
            logger.warning("非管理员用户尝试获取用户列表")
            return []

        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)

            if not config or 'credentials' not in config or 'usernames' not in config['credentials']:  # type: ignore
                logger.error("用户配置文件格式错误")
                return []

            users = []
            for username, user_data in config['credentials']['usernames'].items():  # type: ignore
                users.append({
                    'username': username,
                    'name': user_data.get('name', ''),
                    'email': user_data.get('email', ''),
                    'role': user_data.get('role', 'user')
                })

            logger.debug(f"成功获取 {len(users)} 个用户信息")
            return users
        except Exception as e:
            logger.error(f"获取用户列表失败: {e}")
            return []
    
    def add_user(self, username: str, name: str, email: str, password: str, role: str = 'user') -> bool:
        """添加新用户（仅管理员可用）"""
        if not self.is_admin():
            return False
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            # 检查用户名是否已存在
            if username in config['credentials']['usernames']:  # type: ignore
                return False

            # 添加新用户
            import bcrypt
            hashed_password = bcrypt.hashpw(password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            config['credentials']['usernames'][username] = {  # type: ignore
                'name': name,
                'email': email,
                'password': hashed_password,
                'role': role
            }
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config, file, default_flow_style=False, allow_unicode=True)
            
            # 重新加载配置
            self._load_config()
            
            logger.info(f"已添加新用户: {username}")
            return True
            
        except Exception as e:
            logger.error(f"添加用户失败: {e}")
            return False
    
    def update_user_password(self, username: str, new_password: str) -> bool:
        """更新用户密码（管理员可更新任何用户，普通用户只能更新自己）"""
        current_user = self.get_current_user()
        if not current_user:
            return False
        
        # 权限检查
        if current_user['role'] != 'admin' and current_user['username'] != username:
            return False
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            if username not in config['credentials']['usernames']:  # type: ignore
                return False

            # 更新密码
            import bcrypt
            hashed_password = bcrypt.hashpw(new_password.encode('utf-8'), bcrypt.gensalt()).decode('utf-8')
            config['credentials']['usernames'][username]['password'] = hashed_password  # type: ignore
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config, file, default_flow_style=False, allow_unicode=True)
            
            # 重新加载配置
            self._load_config()
            
            logger.info(f"已更新用户 {username} 的密码")
            return True
            
        except Exception as e:
            logger.error(f"更新密码失败: {e}")
            return False
    
    def delete_user(self, username: str) -> bool:
        """删除用户（仅管理员可用，不能删除自己）"""
        if not self.is_admin():
            return False
        
        current_user = self.get_current_user()
        if current_user and current_user['username'] == username:
            return False  # 不能删除自己
        
        try:
            with open(self.config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
            
            if username not in config['credentials']['usernames']:  # type: ignore
                return False

            # 删除用户
            del config['credentials']['usernames'][username]  # type: ignore
            
            # 保存配置
            with open(self.config_path, 'w', encoding='utf-8') as file:
                yaml.dump(config, file, default_flow_style=False, allow_unicode=True)
            
            # 重新加载配置
            self._load_config()
            
            logger.info(f"已删除用户: {username}")
            return True
            
        except Exception as e:
            logger.error(f"删除用户失败: {e}")
            return False


# 创建全局实例
auth_manager = AuthManager()

__all__ = ['AuthManager', 'auth_manager']
