#!/usr/bin/env python3
"""
测试登录表单状态的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import streamlit as st
from utils.auth_manager import auth_manager

# 模拟session state
class MockSessionState:
    def __init__(self):
        self.data = {}
    
    def get(self, key, default=None):
        return self.data.get(key, default)
    
    def __setattr__(self, key, value):
        if key == 'data':
            super().__setattr__(key, value)
        else:
            self.data[key] = value
    
    def __getattr__(self, key):
        return self.data.get(key)
    
    def __contains__(self, key):
        return key in self.data
    
    def __delitem__(self, key):
        if key in self.data:
            del self.data[key]

def test_login_logout_cycle():
    """测试登录登出循环"""
    print("=== 测试登录登出循环 ===")
    
    try:
        # 设置模拟的session state
        st.session_state = MockSessionState()
        
        print("1. 初始状态 - 未登录")
        print(f"   - 认证状态: {st.session_state.get('authentication_status')}")
        print(f"   - 用户名: {st.session_state.get('username')}")
        print(f"   - 是否认证: {auth_manager.is_authenticated()}")
        
        print("\n2. 模拟登录成功")
        # 模拟登录成功的状态
        st.session_state.authentication_status = True
        st.session_state.username = 'admin'
        st.session_state.name = '系统管理员'
        st.session_state.user_info = {
            'username': 'admin',
            'name': '系统管理员',
            'email': '<EMAIL>',
            'role': 'admin'
        }
        
        print(f"   - 认证状态: {st.session_state.get('authentication_status')}")
        print(f"   - 用户名: {st.session_state.get('username')}")
        print(f"   - 是否认证: {auth_manager.is_authenticated()}")
        print(f"   - 当前用户: {auth_manager.get_current_user()}")
        
        print("\n3. 执行登出")
        auth_manager.logout()
        
        print(f"   - 认证状态: {st.session_state.get('authentication_status')}")
        print(f"   - 用户名: {st.session_state.get('username')}")
        print(f"   - 是否认证: {auth_manager.is_authenticated()}")
        print(f"   - 当前用户: {auth_manager.get_current_user()}")
        
        # 检查所有认证相关的键
        print("\n4. 检查session state中的所有键:")
        for key, value in st.session_state.data.items():
            print(f"   - {key}: {value}")
        
        print("\n🎉 登录登出循环测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_logout_cycle()
