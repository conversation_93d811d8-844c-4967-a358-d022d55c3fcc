"""
配置管理模块，负责处理应用程序的配置信息。
主要功能：
1. 读取和解析配置文件
2. 获取特定模型的配置
3. 更新配置
4. 保存配置到文件
5. 验证配置有效性
"""

from typing import List, Optional
import configparser
import os
import logging
from pathlib import Path
from models.model_config import ModelConfig, ModelInfo
from .error_handler import ConfigError, handle_exceptions

# 设置日志记录器
logger = logging.getLogger(__name__)


class ConfigManager:
    """配置管理器类"""
    def __init__(self):
        """
        初始化配置管理器

        Args:
            config_path: 配置文件路径

        Raises:
            FileNotFoundError: 当配置文件不存在时
        """
        # 环境配置已在env_config模块中处理

        # 更新配置文件路径
        self.config_path = Path(__file__).parent.parent / "settings" / "config.ini"
        self.config = configparser.ConfigParser()
        self.load_config()
        logger.info(f"配置管理器初始化完成，使用配置文件: {self.config_path}")

    @handle_exceptions(context="加载配置文件", reraise=True)
    def load_config(self) -> None:
        """
        加载配置文件

        Raises:
            ConfigError: 当配置文件不存在或格式错误时
        """
        if not os.path.exists(self.config_path):
            raise ConfigError(f"配置文件不存在: {self.config_path}")

        try:
            self.config.read(self.config_path, encoding='utf-8')
            logger.info(f"成功加载配置文件，包含 {len(self.config.sections())} 个模型配置")
        except configparser.Error as e:
            raise ConfigError(f"配置文件格式无效: {str(e)}")

    def get_model_config(self, model_name: str) -> Optional[ModelConfig]:
        """
        获取指定模型的配置
        
        Args:
            model_name: 模型名称
            
        Returns:
            ModelConfig: 模型配置对象，如果模型不存在则返回None
            
        Raises:
            ValueError: 当配置格式无效时
        """
        if model_name not in self.config:
            logger.warning(f"模型配置不存在: {model_name}")
            return None
        section = self.config[model_name]
        try:
            def parse_float(val, default=0.0):
                try:
                    return float(val)
                except Exception:
                    return float(default)
            def parse_int(val, default=0):
                try:
                    return int(float(val))
                except Exception:
                    return int(default)
            def parse_bool(val, default=False):
                if isinstance(val, bool):
                    return val
                if isinstance(val, str):
                    return val.lower() in ("true", "1", "yes")
                return bool(val) if val is not None else default
            tokens = parse_int(section.get('tokens', 2048), 2048)
            temperature = parse_float(section.get('temperature', 0.7), 0.7)
            top = parse_float(section.get('top', 0.9), 0.9)
            choice = parse_bool(section.get('choice', True), True)

            # 从配置文件获取API密钥
            api_key = section.get('api_key', '').strip()

            # 如果没有API密钥，给出友好提示但不阻止加载
            if not api_key:
                logger.warning(
                    f"模型 {model_name} 的API密钥未配置。"
                    f"请在配置文件中设置 api_key"
                )
            # 补全base_url和base_url_list结尾的斜杠
            def ensure_trailing_slash(url):
                if url and not url.endswith('/'):
                    return url + '/'
                return url
            base_url = ensure_trailing_slash(section.get('base_url'))
            base_url_list = [ensure_trailing_slash(x) for x in section.get('base_url_list', '').strip(',').split(',') if x]
            model_list = [x for x in section.get('model_list', '').strip(',').split(',') if x]
            model_info: ModelInfo = {
                "name": section.get('model'),
                "parameters": {
                    "max_tokens": tokens,
                    "temperature": temperature,
                    "top_p": top
                },
                "family": "gpt-4o",
                "functions": [],
                "vision": False,
                "json_output": True,
                "function_calling": True,
                "structured_output": True
            }
            return ModelConfig(
                name=section.get('model'),
                base_url=base_url,
                api_key=api_key,  # 使用从环境变量或配置文件获取的API密钥
                tokens=tokens,
                temperature=temperature,
                top=top,
                choice=choice,
                base_url_list=base_url_list,
                model_list=model_list,
                model_info=model_info
            )
        except Exception as e:
            logger.error(f"解析模型 {model_name} 配置失败: {str(e)}")
            raise ValueError(f"解析配置失败: {str(e)}")

    def get_all_models(self) -> List[str]:
        """
        获取所有配置的模型名称
        
        Returns:
            List[str]: 模型名称列表
        """
        return list(self.config.sections())

    def update_model_config(self, model_name: str, config: ModelConfig) -> None:
        """
        更新模型配置
        
        Args:
            model_name: 模型名称
            config: 新的配置对象
            
        Raises:
            ValueError: 当配置对象无效时
            KeyError: 当模型名称无效时
        """
        try:
            # 验证配置对象类型
            if not isinstance(config, ModelConfig):
                logger.error(f"配置必须是ModelConfig类型#1: {type(config)}")
                raise ValueError(f"配置必须是ModelConfig类型，而不是 {type(config)}")
            
            # 验证模型名称
            if not model_name or not isinstance(model_name, str):
                raise ValueError("无效的模型名称")
            
            # 确保section存在
            if model_name not in self.config:
                self.config.add_section(model_name)
            
            # 更新配置
            self.config[model_name].update({
                'choice': str(config.choice),
                'api_key': str(config.api_key),
                'base_url': str(config.base_url),
                'model': str(config.name),
                'tokens': str(config.tokens),
                'temperature': str(config.temperature),
                'top': str(config.top),
                'base_url_list': ','.join(config.base_url_list) + ',',
                'model_list': ','.join(config.model_list) + ','
            })
            
            logger.info(f"成功更新模型 {model_name} 的配置")
            
        except (ValueError, AttributeError) as e:
            logger.error(f"更新配置失败: {str(e)}")
            raise ValueError(f"更新配置失败: {str(e)}")
        except Exception as e:
            logger.error(f"更新配置时发生错误: {str(e)}")
            raise Exception(f"更新配置时发生错误: {str(e)}")

    def save_config(self) -> None:
        """保存配置到文件"""
        with open(self.config_path, 'w', encoding='utf-8') as f:
            self.config.write(f)

    def validate_config(self) -> List[str]:
        """
        验证配置有效性
        
        Returns:
            List[str]: 错误信息列表，如果配置有效则为空列表
        """
        errors = []
        logger.info("开始验证配置有效性")
        
        for section in self.config.sections():
            try:
                config = self.get_model_config(section)
                if not config:
                    error_msg = f"无法加载模型配置: {section}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                    continue
                
                # 验证配置数据
                try:
                    config.validate()
                    logger.debug(f"模型 {section} 配置验证通过")
                except ValueError as e:
                    error_msg = f"模型 {section} 配置无效: {str(e)}"
                    errors.append(error_msg)
                    logger.error(error_msg)
                
            except Exception as e:
                error_msg = f"验证模型 {section} 时发生未知错误: {str(e)}"
                errors.append(error_msg)
                logger.error(error_msg)
        
        if errors:
            logger.warning(f"配置验证完成，发现 {len(errors)} 个错误")
        else:
            logger.info("配置验证完成，未发现错误")
        
        return errors


# 使用示例
if __name__ == "__main__":
    config_manager = ConfigManager()
    
    # 获取所有模型
    models = config_manager.get_all_models()
    print("可用模型:", models)
    
    # 获取特定模型配置
    for model in models:
        config = config_manager.get_model_config(model)
        print(f"\n{model} 配置:")
        print(f"API Key: {config.api_key}")
        print(f"Base URL: {config.base_url}")
        print(f"Model: {config.model}")
        print(f"Tokens: {config.tokens}")
        
    # 验证配置
    errors = config_manager.validate_config()
    if errors:
        print("\n配置错误:")
        for error in errors:
            print(f"- {error}")