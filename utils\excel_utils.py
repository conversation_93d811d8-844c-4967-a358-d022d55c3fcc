import pandas as pd
from typing import List, Dict, Any

# 定义表头映射
COLUMN_MAPPING = {
    '用例ID': 'case_id',
    '测试目标': 'test_objective',
    '前置条件': 'precondition',
    '操作步骤': 'steps',
    '预期结果': 'expected_result',
    '优先级': 'priority',
    '测试类型': 'test_type',
    '关联需求': 'requirement'
}

def load_excel_testcases(file) -> str:
    """
    从Excel文件加载测试用例并转换为markdown表格格式
    
    Args:
        file: 上传的Excel文件对象
    
    Returns:
        str: 包含测试用例数据的markdown表格字符串
    """
    try:
        # 读取Excel文件
        df = pd.read_excel(file)
        
        # 确保所有必需的列都存在
        required_columns = list(COLUMN_MAPPING.keys())
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Excel文件缺少必需的列: {col}")
        
        # 构建markdown表格
        markdown_lines = []
        
        # 添加表头
        headers = required_columns
        markdown_lines.append("| " + " | ".join(headers) + " |")
        markdown_lines.append("| " + " | ".join(["---"] * len(headers)) + " |")
        
        # 添加数据行，处理每个单元格的内容
        for _, row in df.iterrows():
            row_values = []
            for col in required_columns:
                # 获取单元格值并处理
                val = row.get(col, '')
                if pd.isna(val):  # 处理空值
                    val = ''
                else:
                    # 处理换行符和特殊字符
                    val = str(val).strip().replace('\n', '<br>').replace('|', '\\|')
                row_values.append(val)
            
            # 确保所有值都被正确转换为字符串
            row_str = " | ".join(row_values)
            markdown_lines.append(f"| {row_str} |")
        
        return "\n".join(markdown_lines)
        
    except Exception as e:
        raise Exception(f"读取Excel文件失败: {str(e)}")
