"""基础处理器模块"""

import os
import json
import datetime
import asyncio
import streamlit as st
from typing import List, Optional, Dict, Any

from utils.logger import setup_logger

logger = setup_logger()

class BaseHandler:
    """基础处理器类"""
    
    def __init__(self):
        self.base_dir = "logs"
        
    async def save_results(
        self,
        generator_responses: Optional[List[str]], 
        reviewer_responses: Optional[List[str]], 
        case_list: Optional[List[str]], 
        result_info: Dict[str, Any], 
        is_review_only: bool = False
    ) -> str:
        """
        保存生成和评审结果
        
        Args:
            generator_responses: 生成器的响应列表
            reviewer_responses: 评审器的响应列表
            case_list: 测试用例列表
            result_info: 结果元数据，包含类型、时间戳、模型信息等
            is_review_only: 是否仅评审模式
            
        Returns:
            str: 保存结果的目录路径
            
        Raises:
            ValueError: 当必要的参数缺失或无效时
            IOError: 当文件操作失败时
            Exception: 其他未预期的错误
        """
        try:
            # 参数验证
            if not generator_responses and not is_review_only:
                raise ValueError("生成模式下必须提供生成器响应")
            if is_review_only and not reviewer_responses:
                raise ValueError("评审模式下必须提供评审器响应")
            
            # 确定保存目录
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            output_dir = os.path.join(
                self.base_dir,
                "reviews" if reviewer_responses else "generations",
                timestamp
            )

            # 关联上传日志（如果存在）
            try:
                import streamlit as st
                if hasattr(st.session_state, 'current_upload_id') and st.session_state.current_upload_id:
                    from utils.upload_log_manager import upload_log_manager
                    upload_log_manager.link_to_generation(
                        st.session_state.current_upload_id,
                        timestamp
                    )
                    # 清除当前上传ID
                    st.session_state.current_upload_id = None
            except Exception as e:
                logger.warning(f"关联上传日志失败: {str(e)}")
            os.makedirs(output_dir, exist_ok=True)

            # 获取当前使用的模型信息
            from models.role_manager import role_manager
            
            # 构建统一的结果数据结构
            result_data = {
                "metadata": {
                    "type": "review" if is_review_only else "generation",
                    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "mode": "single" if is_review_only or not reviewer_responses else "dual",
                },
                "model_info": {
                    "generator": role_manager.generator_model if not is_review_only else None,
                    "reviewer": role_manager.reviewer_model if reviewer_responses or is_review_only else None
                },
                "responses": {
                    "generator": generator_responses[0] if isinstance(generator_responses, list) and generator_responses else None,
                    "reviewer": reviewer_responses if reviewer_responses else None
                }
            }

            # 移除无值字段
            self._clean_empty_fields(result_data)

            # 保存结果元数据到JSON
            await self._save_metadata(output_dir, result_data)
            
            # 处理其他格式文件(Markdown和Excel)
            await self._handle_other_formats(
                output_dir,
                generator_responses,
                reviewer_responses,
                case_list,
                result_data
            )
            
            logger.info(f"已保存所有结果到: {output_dir}")
            return output_dir
            
        except Exception as e:
            error_msg = f"保存结果失败: {str(e)}"
            logger.error(error_msg)
            st.error(error_msg)
            raise

    def _clean_empty_fields(self, data: Dict[str, Any]) -> None:
        """
        清理字典中的空值字段
        
        Args:
            data: 需要清理的数据字典
        """
        if not data["model_info"].get("generator"):
            data["model_info"].pop("generator", None)
        if not data["model_info"].get("reviewer"):
            data["model_info"].pop("reviewer", None)
        if not data["responses"].get("generator"):
            data["responses"].pop("generator", None)
        if not data["responses"].get("reviewer"):
            data["responses"].pop("reviewer", None)

    async def _save_metadata(self, output_dir: str, result_data: Dict[str, Any]) -> None:
        """
        保存结果元数据到JSON文件
        
        Args:
            output_dir: 输出目录路径
            result_data: 要保存的数据
            
        Raises:
            IOError: 当文件写入失败时
        """
        result_path = os.path.join(output_dir, "result.json")
        try:
            with open(result_path, "w", encoding="utf-8") as f:
                json.dump(result_data, f, ensure_ascii=False, indent=2, default=str)
            logger.info(f"结果元数据已保存到: {result_path}")
        except Exception as e:
            raise IOError(f"保存结果元数据失败: {str(e)}")

    async def _handle_other_formats(
        self,
        output_dir: str,
        generator_responses: Optional[List[str]],
        reviewer_responses: Optional[List[str]],
        case_list: Optional[List[str]],
        result_data: Dict[str, Any]
    ) -> None:
        """
        处理其他格式文件的生成(Markdown和Excel)
        
        Args:
            output_dir: 输出目录路径
            generator_responses: 生成器响应
            reviewer_responses: 评审器响应
            case_list: 测试用例列表
            result_data: 结果数据
        """
        try:
            # 获取下载管理器
            download_manager = st.session_state.get('download_manager')
            if not download_manager:
                logger.warning("下载管理器未初始化，跳过其他格式文件的生成")
                return
                
            logger.info("开始生成其他格式文件...")
            if not hasattr(download_manager, 'save_and_generate_all_files'):
                logger.warning("下载管理器缺少必要的方法，跳过其他格式文件的生成")
                return
                
            method = getattr(download_manager, 'save_and_generate_all_files')
            args = (
                generator_responses,
                reviewer_responses,
                output_dir,
                case_list,
                result_data,
            )
            
            # 执行文件生成
            if asyncio.iscoroutinefunction(method):
                await method(*args)
            else:
                method(*args)
                
            logger.info("其他格式文件生成完成")
            
        except Exception as e:
            logger.warning(f"生成其他格式文件时出错: {str(e)}")
            # 不抛出异常，继续执行
