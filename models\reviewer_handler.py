"""评审处理模块"""

import streamlit as st
from typing import Optional, List, Tuple
import datetime
import asyncio

from models.base_handler import BaseHandler
from models import model_manager
from models.result_parser import ResultParser
from utils.logger import setup_logger

logger = setup_logger()

class ReviewerHandler(BaseHandler):
    """评审处理类"""
    
    async def handle_review(
        self,
        test_cases: str,
        system_message: str,
        model_name: str,
        review_count: int
    ) -> <PERSON><PERSON>[List[str], Optional[str]]:
        """处理评审过程"""
        if not test_cases:
            return [], "请先提供要评审的测试用例"
            
        try:
            review_results = []
            current_cases = test_cases
              # 检查模型和其初始化状态
            logger.info(f"准备使用模型 {model_name} 进行评审，当前已加载的模型: {list(model_manager.models.keys())}")
            
            if not hasattr(model_manager, 'models'):
                raise ValueError("模型管理器未正确初始化，models属性不存在")
                
            if model_name not in model_manager.models:
                raise ValueError(f"评审模型 {model_name} 未初始化 (可用模型: {list(model_manager.models.keys())})")
                
            logger.info(f"开始使用模型 {model_name}:{model_name}评审测试用例")
            logger.info(f"评审轮次: {review_count}, 测试用例长度: {len(test_cases)}")
            
            # 执行多轮评审
            for round_num in range(review_count):
                logger.info(f"开始第 {round_num + 1}/{review_count} 轮评审")
                
                # 构建评审消息并调用模型
                messages = [
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": current_cases}
                ]
                logger.debug(f"第 {round_num + 1} 轮评审消息内容messages:\n {messages}")
                try:
                    review_response = await model_manager.generate_completion(
                        model_name,
                        messages
                    )
                except Exception as e:
                    logger.error(f"调用模型评审失败: {str(e)}")
                    raise
                    
                # 解析评审结果
                final_review = ResultParser.extract_final_review_markdown(review_response)
                logger.debug(f"第 {round_num + 1} 轮评审结果解析完成{final_review}")
                
                # 保存并更新
                review_results.append(review_response)
                if final_review:
                    current_cases = final_review
                    logger.debug(f"使用最终评审结果作为下一轮输入:\n{current_cases}")
                else:
                    logger.warning("未找到最终评审结果表格，使用原始响应")
                    current_cases = review_response
                    
            logger.info("所有评审轮次已完成")
            return review_results, None
            
        except Exception as e:
            error_msg = f"评审失败: {str(e)}"
            logger.error(error_msg)
            return [], error_msg