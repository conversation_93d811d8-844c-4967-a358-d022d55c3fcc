import logging
import os
from logging.handlers import RotatingFileHandler
import sys
import threading
from datetime import datetime

class ThreadSafeHandler(RotatingFileHandler):
    """线程安全的日志处理器"""

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self._lock = threading.Lock()

    def emit(self, record):
        """使用线程锁保证线程安全"""
        if self._lock.acquire(timeout=2):  # 设置获取锁的超时时间
            try:
                super().emit(record)
            finally:
                self._lock.release()


class LogFilter(logging.Filter):
    """日志过滤器，减少冗余日志"""

    def __init__(self):
        super().__init__()
        self.last_messages = {}
        self.repeat_count = {}
        self.max_repeats = 3  # 最多重复3次相同消息

    def filter(self, record):
        """过滤重复的日志消息"""
        message_key = f"{record.levelname}:{record.module}:{record.getMessage()}"

        # 检查是否是重复消息
        if message_key in self.last_messages:
            self.repeat_count[message_key] = self.repeat_count.get(message_key, 0) + 1
            if self.repeat_count[message_key] > self.max_repeats:
                return False  # 过滤掉重复消息
        else:
            self.last_messages[message_key] = True
            self.repeat_count[message_key] = 1

        return True

def setup_logger(
    name: str = 'app',
    log_dir: str = None,
    log_level: str = None,
    max_bytes: int = 5 * 1024 * 1024,  # 5MB (减少文件大小)
    backup_count: int = 3,  # 减少备份数量
    log_format: str = 'json',  # 支持 'json' 或 'text'
    log_to_console: bool = True,
    log_to_file: bool = True
) -> logging.Logger:
    """
    设置日志记录器
    
    参数:
        name: 日志记录器名称
        log_dir: 日志文件目录
        log_level: 日志级别（如 'INFO', 'DEBUG'）
        max_bytes: 日志文件最大大小（字节）
        backup_count: 日志文件备份数量
        log_format: 日志格式（'json' 或 'text'）
        log_to_console: 是否输出到控制台
        log_to_file: 是否输出到文件
    """
    try:
        # 从环境配置获取默认值
        if log_dir is None:
            try:
                from .env_config import env_config
                log_dir = env_config.get_log_dir()
            except ImportError:
                log_dir = 'logs/applog'

        if log_level is None:
            try:
                from .env_config import env_config
                log_level = env_config.get_log_level()
            except ImportError:
                log_level = 'INFO'  # 恢复正常的INFO级别

        logger = logging.getLogger(name)
        if logger.handlers:  # 避免重复初始化
            return logger
            
        # 设置日志级别
        level = getattr(logging, log_level.upper(), logging.INFO)
        logger.setLevel(level)
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 定义日志格式
        if log_format == 'json':
            formatter = logging.Formatter(
                '{"timestamp": "%(asctime)s", "level": "%(levelname)s", '
                '"module": "%(module)s", "lineno": %(lineno)d, '
                '"function": "%(funcName)s", "message": "%(message)s", '
                '"thread": "%(threadName)s"}'
            )
        else:  # 默认文本格式
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(module)s:%(lineno)d - %(funcName)s - %(message)s'
            )
        formatter.datefmt = '%Y-%m-%d %H:%M:%S'
        
        # 动态生成日志文件名
        current_date = datetime.now().strftime("%Y%m%d")  # 格式如 20231025
        log_filename = f"{name}_{current_date}.log"
        
        # 创建日志过滤器
        log_filter = LogFilter()

        # 添加文件处理器
        if log_to_file:
            file_handler = ThreadSafeHandler(
                os.path.join(log_dir, log_filename),
                maxBytes=max_bytes,
                backupCount=backup_count,
                encoding='utf-8'
            )
            file_handler.setFormatter(formatter)
            file_handler.addFilter(log_filter)  # 添加过滤器
            logger.addHandler(file_handler)
        
        # 添加控制台处理器
        if log_to_console:
            console_handler = logging.StreamHandler(sys.stdout)
            console_handler.setFormatter(formatter)
            logger.addHandler(console_handler)
        
        return logger
        
    except Exception as e:
        # 如果日志设置失败，创建一个基本的控制台logger
        basic_logger = logging.getLogger(name)
        basic_logger.setLevel(logging.INFO)
        handler = logging.StreamHandler(sys.stdout)
        handler.setFormatter(logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s'))
        basic_logger.addHandler(handler)
        basic_logger.error(f"日志设置失败: {str(e)}")
        return basic_logger