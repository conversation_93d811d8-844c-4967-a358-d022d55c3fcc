"""
UI布局模块，负责管理应用程序的页面布局。
"""

import streamlit as st
from typing import Optional, Dict, Any
from .components import  render_jquery
from .styles import apply_base_styles, get_404_styles


def setup_page_config() -> None:
    """设置页面基本配置"""
    st.set_page_config(
        page_title="测试用例生成工具",
        page_icon="✨",
        layout="wide",
        initial_sidebar_state="auto"
    )
    
    # 应用基础样式
    st.markdown(f'<style>{apply_base_styles()}</style>', unsafe_allow_html=True)
    
    # 移除页脚
    render_jquery()


def render_header() -> None:
    """渲染页面头部"""
    cols = st.columns([8, 2])

    with cols[0]:
        st.title("✨ AI测试用例生成工具")
        st.caption("基于大语言模型的测试用例生成工具，支持多种测试类型和优先级设置。")

    with cols[1]:
        # 渲染右上角的用户信息和登出按钮
        render_header_user_info()


def render_header_user_info() -> None:
    """渲染页面头部右上角的用户信息和登出按钮"""
    try:
        from utils.auth_manager import get_global_auth_manager
        auth_manager = get_global_auth_manager()

        if auth_manager.is_authenticated():
            user_info = auth_manager.get_current_user()
            if user_info:
                # 创建用户信息容器，使用右对齐
                st.markdown("""
                <style>
                .header-user-info {
                    text-align: right;
                    padding: 15px 10px;
                    margin-top: 20px;
                    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                    border-radius: 10px;
                    border: 1px solid #e1e8ed;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }
                .user-name {
                    font-size: 16px;
                    font-weight: bold;
                    color: #2c3e50;
                    margin-bottom: 5px;
                }
                .user-role {
                    font-size: 12px;
                    color: #7f8c8d;
                    margin-bottom: 10px;
                }
                .logout-btn {
                    margin-top: 5px;
                }
                </style>
                """, unsafe_allow_html=True)

                # 用户信息显示
                st.markdown(f"""
                <div class="header-user-info">
                    <div class="user-name">👤 {user_info['name']}</div>
                    <div class="user-role">{'🔧 管理员' if user_info['role'] == 'admin' else '👤 普通用户'}</div>
                </div>
                """, unsafe_allow_html=True)

                # 登出按钮
                if st.button("🚪 登出", type="secondary", key="header_logout_btn"):
                    auth_manager.logout()
                    st.success("登出成功！正在跳转到登录页面...")
                    st.rerun()
    except ImportError:
        # 如果认证模块未加载，不显示任何内容
        pass
    except Exception as e:
        # 静默处理错误，避免影响页面显示
        pass


def render_sidebar() -> Dict[str, Any]:
    """
    渲染侧边栏，显示当前启用模型、模型状态、历史记录下载、帮助等实用信息

    Returns:
        Dict[str, Any]: 侧边栏配置
    """
    sidebar_config = {}
    with st.sidebar:
        st.markdown("### 🛠️ 工具信息")
        
        # 显示当前启用模型
        from utils.config_manager import ConfigManager
        config_manager = ConfigManager()
        enabled_models = []
        for section in config_manager.config.sections():
            if config_manager.config[section].get('choice', 'False') in ['True', 'true', '1']:
                enabled_models.append(section)
        if enabled_models:
            st.markdown(f"**当前启用模型：** {', '.join(enabled_models)}")
        else:
            st.warning("未启用任何模型，请在配置中启用至少一个模型！")
        
        # 显示模型状态
        for section in enabled_models:
            api_key = config_manager.config[section].get('api_key', '')
            base_url = config_manager.config[section].get('base_url', '')
            st.markdown(f"- **{section}**  ")
            st.caption(f"API Key: {'已配置' if api_key else '未配置'} | Base URL: {base_url}")
        
        st.markdown("---")
        
        # 历史记录下载入口（占位，实际功能可后续实现）
        st.markdown("#### 📂 历史记录")
        st.info("可在 downloads 文件夹中查看和下载历史生成的用例和模型回复。")
        
        st.markdown("---")
        
        # 帮助与反馈
        st.markdown("#### ❓ 帮助与反馈")
        st.markdown("如有问题请查阅[使用文档](README.md)或联系开发者。")
    
    return sidebar_config


def render_sidebar_menu() -> str:
    """
    渲染侧边栏菜单，支持模型设置、提示词设置和测试用例生成的切换
    使用URL参数确保页面刷新后保持当前菜单状态

    Returns:
        str: 当前选择的菜单项
    """
    # 基础菜单选项
    menu_options = [
        "模型设置",
        "提示词设置",
        "测试用例生成",
        "测试用例评审",
        "历史生成数据",
        "个人设置"
    ]

    # 如果是管理员，添加用户管理选项
    try:
        from utils.auth_manager import auth_manager
        if auth_manager.is_admin():
            menu_options.insert(-1, "用户管理")  # 在个人设置前插入
    except ImportError:
        pass  # 如果认证模块未加载，忽略

    # 从URL参数获取当前页面
    query_params = st.query_params
    current_page = query_params.get("page", "模型设置")



    # 确保URL参数中的页面是有效的
    if current_page not in menu_options:
        current_page = "模型设置"

    # 获取当前页面的索引
    current_index = menu_options.index(current_page)

    # 渲染菜单
    selected_menu = st.sidebar.radio(
        "功能菜单",
        options=menu_options,
        index=current_index,
        key="menu_radio"
    )

    # 如果用户选择了不同的菜单，更新URL参数
    if selected_menu != current_page:
        st.query_params.page = selected_menu
        st.rerun()  # 重新运行以更新URL

    return selected_menu


def render_404_page(message: Optional[str] = None) -> None:
    """
    渲染404错误页面

    Args:
        message: 错误信息
    """
    styles = get_404_styles()
    
    st.markdown(f'<style>{styles["background"]}</style>', unsafe_allow_html=True)
    
    if message:
        st.markdown(styles['text'].format(message), unsafe_allow_html=True)
    else:
        st.markdown(styles['text'].format("页面不存在"), unsafe_allow_html=True)


def render_error_message(error: str) -> None:
    """
    渲染错误信息

    Args:
        error: 错误信息
    """
    st.error(f"错误: {error}")


def render_success_message(message: str) -> None:
    """
    渲染成功信息

    Args:
        message: 成功信息
    """
    st.success(message)


def render_info_message(message: str) -> None:
    """
    渲染提示信息

    Args:
        message: 提示信息
    """
    st.info(message)


def render_warning_message(message: str) -> None:
    """
    渲染警告信息

    Args:
        message: 警告信息
    """
    st.warning(message)


def render_progress_bar(progress: float, text: str) -> None:
    """
    渲染进度条

    Args:
        progress: 进度值(0-1)
        text: 进度文本
    """
    progress_bar = st.progress(0)
    progress_bar.progress(progress)
    st.write(text)