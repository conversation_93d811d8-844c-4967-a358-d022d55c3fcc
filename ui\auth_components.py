"""
认证相关的UI组件

提供登录页面、用户管理界面等UI组件
"""

import streamlit as st
import pandas as pd
from typing import Optional
from utils.auth_manager import auth_manager
from utils.logger import setup_logger

logger = setup_logger()

def render_login_page():
    """渲染登录页面"""
    st.markdown("""
    <style>
    .login-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 2rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .login-title {
        text-align: center;
        color: #1f77b4;
        margin-bottom: 2rem;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 居中显示登录表单
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown('<div class="login-container">', unsafe_allow_html=True)
        
        # 标题和图标
        st.markdown('<h2 class="login-title">🔐 AI测试用例生成工具</h2>', unsafe_allow_html=True)
        st.markdown('<p style="text-align: center; color: #666;">请登录以继续使用</p>', unsafe_allow_html=True)
        
        # 登录表单
        name, authentication_status, username = auth_manager.login()
        
        # 显示默认账号信息（仅在开发环境）
        with st.expander("💡 默认账号信息"):
            st.info("""
            **默认管理员账号：**
            - 用户名: admin
            - 密码: admin123
            
            首次登录后请及时修改密码！
            """)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    return name, authentication_status, username

def render_user_management():
    """渲染用户管理页面（仅管理员可见）"""
    # 调试信息
    current_user = auth_manager.get_current_user()
    logger.debug(f"用户管理页面 - 当前用户: {current_user}")
    logger.debug(f"用户管理页面 - 是否管理员: {auth_manager.is_admin()}")

    if not auth_manager.is_admin():
        st.error("❌ 权限不足，只有管理员可以访问用户管理功能")
        if current_user:
            st.info(f"当前用户: {current_user.get('username', '未知')} (角色: {current_user.get('role', '未知')})")
        else:
            st.warning("无法获取当前用户信息，请重新登录")
        return

    st.markdown("### 👥 用户管理")

    # 创建标签页
    tab1, tab2, tab3 = st.tabs(["📋 用户列表", "➕ 添加用户", "🔧 用户操作"])

    with tab1:
        render_user_list()

    with tab2:
        render_add_user_form()

    with tab3:
        render_user_operations()

def render_user_list():
    """渲染用户列表"""
    st.markdown("#### 📋 当前用户列表")

    users = auth_manager.get_all_users()
    logger.debug(f"获取到的用户列表: {users}")

    if not users:
        st.warning("暂无用户数据或获取用户列表失败")
        # 显示调试信息
        if auth_manager.is_admin():
            st.info("您是管理员，但无法获取用户列表。请检查用户配置文件。")
        return
    
    # 转换为DataFrame显示
    df = pd.DataFrame(users)
    df['角色'] = df['role'].map({'admin': '管理员', 'user': '普通用户'})
    
    # 显示表格
    display_df = df[['username', 'name', 'email', '角色']].copy()
    display_df.columns = ['用户名', '姓名', '邮箱', '角色']
    
    st.dataframe(display_df, use_container_width=True)
    
    # 统计信息
    total_users = len(users)
    admin_count = len([u for u in users if u['role'] == 'admin'])
    user_count = total_users - admin_count
    
    col1, col2, col3 = st.columns(3)
    with col1:
        st.metric("总用户数", total_users)
    with col2:
        st.metric("管理员", admin_count)
    with col3:
        st.metric("普通用户", user_count)

def render_add_user_form():
    """渲染添加用户表单"""
    st.markdown("#### ➕ 添加新用户")
    
    with st.form("add_user_form"):
        col1, col2 = st.columns(2)
        
        with col1:
            username = st.text_input("用户名*", placeholder="输入用户名")
            name = st.text_input("姓名*", placeholder="输入真实姓名")
        
        with col2:
            email = st.text_input("邮箱*", placeholder="<EMAIL>")
            role = st.selectbox("角色*", ["user", "admin"], 
                               format_func=lambda x: "普通用户" if x == "user" else "管理员")
        
        password = st.text_input("密码*", type="password", placeholder="输入初始密码")
        confirm_password = st.text_input("确认密码*", type="password", placeholder="再次输入密码")
        
        submitted = st.form_submit_button("添加用户", type="primary")
        
        if submitted:
            # 验证输入
            if not all([username, name, email, password]):
                st.error("请填写所有必填字段")
                return
            
            if password != confirm_password:
                st.error("两次输入的密码不一致")
                return
            
            if len(password) < 6:
                st.error("密码长度至少6位")
                return
            
            # 添加用户
            if auth_manager.add_user(username, name, email, password, role):
                st.success(f"✅ 用户 {username} 添加成功！")
                st.rerun()
            else:
                st.error("❌ 添加用户失败，用户名可能已存在")

def render_user_operations():
    """渲染用户操作（修改密码、删除用户）"""
    st.markdown("#### 🔧 用户操作")
    
    users = auth_manager.get_all_users()
    if not users:
        st.info("暂无用户数据")
        return
    
    # 选择用户
    user_options = {f"{u['username']} ({u['name']})": u['username'] for u in users}
    selected_user_display = st.selectbox("选择用户", list(user_options.keys()))
    selected_username = user_options[selected_user_display]
    
    # 操作选项
    operation = st.radio("选择操作", ["修改密码", "删除用户"])
    
    if operation == "修改密码":
        render_change_password_form(selected_username)
    elif operation == "删除用户":
        render_delete_user_form(selected_username)

def render_change_password_form(username: str):
    """渲染修改密码表单"""
    st.markdown(f"##### 🔑 修改用户 `{username}` 的密码")
    
    with st.form(f"change_password_{username}"):
        new_password = st.text_input("新密码", type="password", placeholder="输入新密码")
        confirm_password = st.text_input("确认新密码", type="password", placeholder="再次输入新密码")
        
        submitted = st.form_submit_button("修改密码", type="primary")
        
        if submitted:
            if not new_password:
                st.error("请输入新密码")
                return
            
            if new_password != confirm_password:
                st.error("两次输入的密码不一致")
                return
            
            if len(new_password) < 6:
                st.error("密码长度至少6位")
                return
            
            if auth_manager.update_user_password(username, new_password):
                st.success(f"✅ 用户 {username} 的密码修改成功！")
            else:
                st.error("❌ 修改密码失败")

def render_delete_user_form(username: str):
    """渲染删除用户表单"""
    current_user = auth_manager.get_current_user()
    
    if current_user and current_user['username'] == username:
        st.warning("⚠️ 不能删除自己的账号")
        return
    
    st.markdown(f"##### 🗑️ 删除用户 `{username}`")
    st.warning("⚠️ 删除操作不可恢复，请谨慎操作！")
    
    # 需要输入确认文本
    confirm_text = st.text_input("请输入用户名以确认删除", placeholder=f"输入 {username} 确认删除")
    
    if st.button("确认删除", type="secondary"):
        if confirm_text != username:
            st.error("确认文本不正确")
            return
        
        if auth_manager.delete_user(username):
            st.success(f"✅ 用户 {username} 删除成功！")
            st.rerun()
        else:
            st.error("❌ 删除用户失败")

def render_user_profile():
    """渲染用户个人信息页面"""
    st.markdown("### 👤 个人信息")

    user_info = auth_manager.get_current_user()
    logger.debug(f"个人信息页面 - 用户信息: {user_info}")

    if not user_info:
        st.error("获取用户信息失败")

        # 显示调试信息
        auth_status = st.session_state.get('authentication_status')
        username = st.session_state.get('username')
        st.info(f"调试信息 - 认证状态: {auth_status}, 用户名: {username}")

        if auth_status and username:
            st.warning("检测到您已登录，但用户信息丢失。请尝试刷新页面或重新登录。")
        else:
            st.warning("您可能未登录，请返回登录页面。")
        return
    
    # 显示用户信息
    col1, col2 = st.columns(2)
    
    with col1:
        st.info(f"""
        **用户名:** {user_info['username']}
        **姓名:** {user_info['name']}
        """)
    
    with col2:
        st.info(f"""
        **邮箱:** {user_info['email']}
        **角色:** {'管理员' if user_info['role'] == 'admin' else '普通用户'}
        """)
    
    # 修改密码
    st.markdown("#### 🔑 修改密码")
    
    with st.form("change_my_password"):
        new_password = st.text_input("新密码", type="password")
        confirm_password = st.text_input("确认新密码", type="password")
        
        submitted = st.form_submit_button("修改密码", type="primary")
        
        if submitted:
            if not new_password:
                st.error("请输入新密码")
                return
            
            if new_password != confirm_password:
                st.error("两次输入的密码不一致")
                return
            
            if len(new_password) < 6:
                st.error("密码长度至少6位")
                return
            
            if auth_manager.update_user_password(user_info['username'], new_password):
                st.success("✅ 密码修改成功！")
            else:
                st.error("❌ 修改密码失败")

def render_auth_sidebar():
    """渲染认证相关的侧边栏内容"""
    if auth_manager.is_authenticated():
        user_info = auth_manager.get_current_user()
        if user_info:
            st.sidebar.markdown("---")
            st.sidebar.markdown("### 👤 当前用户")
            st.sidebar.info(f"""
            **{user_info['name']}**
            {user_info['username']}
            {'🔧 管理员' if user_info['role'] == 'admin' else '👤 普通用户'}
            """)
            
            # 登出按钮
            if st.sidebar.button("🚪 登出", type="secondary"):
                auth_manager.logout()
                st.rerun()

__all__ = [
    'render_login_page',
    'render_user_management', 
    'render_user_profile',
    'render_auth_sidebar'
]
