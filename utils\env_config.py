"""
环境配置管理模块
统一管理环境变量和配置文件的读取
"""

import os
from typing import Optional, Dict, Any
from pathlib import Path
from dotenv import load_dotenv
import logging

logger = logging.getLogger(__name__)


class EnvConfig:
    """环境配置管理器"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._load_env()
            self._initialized = True
    
    def _load_env(self):
        """加载环境变量"""
        # 尝试从多个位置加载.env文件
        env_paths = [
            Path.cwd() / '.env',
            Path(__file__).parent.parent / '.env',
            Path.cwd() / '.env.local'
        ]
        
        for env_path in env_paths:
            if env_path.exists():
                load_dotenv(env_path)
                logger.info(f"已加载环境变量文件: {env_path}")
                break
        else:
            logger.warning("未找到.env文件，将使用系统环境变量")
    
    def get_api_key(self, model_name: str, silent: bool = False) -> Optional[str]:
        """
        获取指定模型的API密钥

        Args:
            model_name: 模型名称 (如 'deepseek', 'qwen')
            silent: 是否静默模式（不输出警告日志）

        Returns:
            API密钥字符串，如果未找到则返回None
        """
        env_key = f"{model_name.upper()}_API_KEY"
        api_key = os.getenv(env_key)

        if not api_key and not silent:
            logger.debug(f"环境变量 {env_key} 中未找到模型 {model_name} 的API密钥")

        return api_key
    
    def get_base_url(self, model_name: str) -> Optional[str]:
        """
        获取指定模型的基础URL
        
        Args:
            model_name: 模型名称
            
        Returns:
            基础URL字符串，如果未找到则返回None
        """
        env_key = f"{model_name.upper()}_BASE_URL"
        return os.getenv(env_key)
    
    def get_log_level(self) -> str:
        """获取日志级别"""
        return os.getenv('LOG_LEVEL', 'INFO')
    
    def get_log_dir(self) -> str:
        """获取日志目录"""
        return os.getenv('LOG_DIR', 'logs')
    
    def get_streamlit_config(self) -> Dict[str, Any]:
        """获取Streamlit配置"""
        return {
            'server_port': int(os.getenv('STREAMLIT_SERVER_PORT', '8501')),
            'server_address': os.getenv('STREAMLIT_SERVER_ADDRESS', 'localhost'),
            'max_upload_size': int(os.getenv('STREAMLIT_MAX_UPLOAD_SIZE', '10'))
        }
    
    def validate_required_keys(self, required_models: list) -> Dict[str, bool]:
        """
        验证必需的API密钥是否存在
        
        Args:
            required_models: 必需的模型列表
            
        Returns:
            字典，键为模型名，值为是否存在API密钥
        """
        results = {}
        for model in required_models:
            api_key = self.get_api_key(model)
            results[model] = bool(api_key and api_key.strip())
        
        return results


# 创建全局实例
env_config = EnvConfig()

__all__ = ['EnvConfig', 'env_config']
