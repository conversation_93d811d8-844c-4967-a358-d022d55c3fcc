"""
模型管理模块，负责创建和管理LLM模型实例。
"""

import asyncio
import json
from typing import Dict, List, Optional, Any, Union, AsyncGenerator
import aiohttp

from .model_config import ModelConfig
from utils.logger import setup_logger


# 设置日志记录器
logger = setup_logger()


class ModelManager:
    """模型管理器类"""
    
    def __init__(self):
        """
        初始化模型管理器
        
        初始化空的模型和会话字典，为后续的模型管理做准备。
        """
        self.models: Dict[str, ModelConfig] = {}
        self.sessions: Dict[str, aiohttp.ClientSession] = {}
        logger.info("模型管理器初始化完成")
        
    async def initialize(self) -> None:
        """
        初始化异步会话
        
        为每个已配置的模型创建HTTP会话。
        
        Raises:
            Exception: 当创建会话失败时
        """
        if not self.models:
            logger.warning("没有已配置的模型")
            return
            
        initialization_errors = []
        for model_name, model_config in self.models.items():
            try:
                # 如果已有会话且未关闭，跳过
                if model_name in self.sessions and not self.sessions[model_name].closed:
                    logger.debug(f"模型 {model_name} 会话已存在且有效，跳过初始化")
                    continue
                    
                logger.info(f"正在初始化模型 {model_name} 的会话")
                await self._initialize_model_session(model_name, model_config)
                
            except Exception as e:
                error_msg = f"初始化模型 {model_name} 失败: {str(e)}"
                logger.error(error_msg)
                initialization_errors.append(error_msg)
                
        if initialization_errors:
            raise Exception(f"部分模型初始化失败:\n" + "\n".join(initialization_errors))
    
    async def close(self) -> None:
        """
        关闭所有异步会话

        安全地关闭所有活动的HTTP会话，并清理会话字典。
        即使某个会话关闭失败，也会继续尝试关闭其他会话。
        """
        close_errors = []

        # 使用 asyncio.gather 并发关闭所有会话
        async def close_session(model_name, session):
            try:
                logger.info(f"正在关闭模型 {model_name} 的会话")
                if not session.closed:
                    await session.close()
                # 确认会话是否确实已关闭
                if session.closed:
                    logger.debug(f"模型 {model_name} 会话已关闭")
                else:
                    error_msg = f"模型 {model_name} 会话未能成功关闭"
                    logger.error(error_msg)
                    close_errors.append(error_msg)
            except (ConnectionError, TimeoutError) as e:
                # 处理特定类型的异常
                error_msg = f"网络错误：关闭模型 {model_name} 会话时出错: {str(e)}"
                logger.error(error_msg)
                close_errors.append(error_msg)
            except Exception as e:
                # 捕获其他未知异常
                error_msg = f"未知错误：关闭模型 {model_name} 会话时出错: {str(e)}"
                logger.error(error_msg)
                close_errors.append(error_msg)

        # 并发关闭所有会话
        tasks = [close_session(model_name, session) for model_name, session in self.sessions.items()]
        await asyncio.gather(*tasks)

        self.sessions.clear()
        logger.debug("close被调用",stacklevel=3 )
        logger.info("所有会话已关闭,标记1")

        if close_errors:
            logger.warning(f"关闭会话时发生 {len(close_errors)} 个错误")
    
    def _validate_model_config(self, model_config: ModelConfig) -> None:
        """
        验证模型配置的有效性
        
        Args:
            model_config: 要验证的模型配置
            
        Raises:
            ValueError: 当配置无效时
        """
        if not isinstance(model_config, ModelConfig):
            logger.error("配置必须是ModelConfig类型#2")
            raise ValueError("配置必须是ModelConfig类型#2")
            
        if not model_config.name:
            raise ValueError("模型名称不能为空")
            
        if not model_config.base_url:
            raise ValueError("base_url不能为空")
            
        if not model_config.api_key:
            raise ValueError("api_key不能为空")
            
        if not isinstance(model_config.model_info, dict):
            raise ValueError("model_info必须是字典类型")
            
        required_fields = ["name", "parameters"]
        for field in required_fields:
            if field not in model_config.model_info:
                raise ValueError(f"model_info缺少必需字段: {field}")
                
        parameters = model_config.model_info["parameters"]
        if not isinstance(parameters, dict):
            raise ValueError("parameters必须是字典类型")
            
        required_params = ["max_tokens", "temperature", "top_p"]
        for param in required_params:
            if param not in parameters:
                raise ValueError(f"parameters缺少必需参数: {param}")
    
    def add_model(self, model_name: str, model_config: ModelConfig) -> None:
        """
        添加模型配置
        
        Args:
            model_name: 模型名称
            model_config: 模型配置
            
        Raises:
            ValueError: 当模型名称为空或配置无效时
            Exception: 当添加模型时发生其他错误
        """
        try:
            # 验证模型名称
            if not model_name or not isinstance(model_name, str):
                raise ValueError("模型名称无效")
            
            # 验证模型配置
            self._validate_model_config(model_config)
            
            # 如果模型已存在，先移除旧的配置
            if model_name in self.models:
                logger.warning(f"模型 {model_name} 已存在，将被覆盖")
                self.remove_model(model_name)
            
            # 添加新配置
            self.models[model_name] = model_config
            logger.info(f"成功添加模型 {model_name}")
            
        except ValueError as e:
            logger.error(f"添加模型 {model_name} 失败，配置无效: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"添加模型 {model_name} 时发生错误: {str(e)}")
            raise Exception(f"添加模型失败: {str(e)}")
    
    def remove_model(self, model_name: str) -> None:
        """
        移除模型配置
        
        Args:
            model_name: 模型名称
            
        Raises:
            Exception: 当移除模型时发生错误
        """
        try:
            # 检查模型是否存在
            if model_name not in self.models:
                logger.warning(f"尝试移除不存在的模型: {model_name}")
                return
            
            # 移除模型配置
            self.models.pop(model_name)
            logger.info(f"已移除模型 {model_name} 的配置")
            
            # 关闭并移除会话
            if model_name in self.sessions:
                session = self.sessions[model_name]
                if not session.closed:
                    logger.debug(f"正在关闭模型 {model_name} 的会话")
                    asyncio.create_task(session.close())
                self.sessions.pop(model_name)
                logger.info(f"已移除模型 {model_name} 的会话")
                
        except Exception as e:
            logger.error(f"移除模型 {model_name} 时发生错误: {str(e)}")
            raise Exception(f"移除模型失败: {str(e)}")
    
    async def update_model_config(
        self,
        model_name: str,
        updates: Dict[str, Any]
    ) -> None:
        """
        更新模型配置
        
        Args:
            model_name: 模型名称
            updates: 更新的配置项
            
        Raises:
            KeyError: 当模型不存在时
            ValueError: 当更新的配置项无效时
            Exception: 当更新配置时发生其他错误
        """
        try:
            # 验证模型是否存在
            if model_name not in self.models:
                raise KeyError(f"模型不存在#1: {model_name}")
            
            logger.info(f"开始更新模型 {model_name} 的配置")
            config = self.models[model_name]
            session_needs_update = False
            
            # 验证并更新API密钥
            if "api_key" in updates:
                new_api_key = updates["api_key"]
                if not isinstance(new_api_key, str) or not new_api_key.strip():
                    raise ValueError("API密钥不能为空")
                config.api_key = new_api_key.strip()
                session_needs_update = True
                logger.debug(f"已更新模型 {model_name} 的API密钥")
            
            # 验证并更新基础URL
            if "base_url" in updates:
                new_base_url = updates["base_url"]
                if not isinstance(new_base_url, str) or not new_base_url.strip():
                    raise ValueError("base_url不能为空")
                config.base_url = new_base_url.strip()
                session_needs_update = True
                logger.debug(f"已更新模型 {model_name} 的base_url")
            
            # 验证并更新模型名称
            if "model" in updates:
                new_model = updates["model"]
                if not isinstance(new_model, str) or not new_model.strip():
                    raise ValueError("模型名称不能为空")
                config.model_info["name"] = new_model.strip()
                logger.debug(f"已更新模型 {model_name} 的模型名称")
            
            # 验证并更新模型参数
            if "parameters" in updates:
                new_params = updates["parameters"]
                if not isinstance(new_params, dict):
                    raise ValueError("parameters必须是字典类型")
                
                # 验证参数值
                if "max_tokens" in new_params:
                    if not isinstance(new_params["max_tokens"], int) or new_params["max_tokens"] <= 0:
                        raise ValueError("max_tokens必须是正整数")
                
                if "temperature" in new_params:
                    if not isinstance(new_params["temperature"], (int, float)) or \
                       not 0 <= new_params["temperature"] <= 2:
                        raise ValueError("temperature必须在0到2之间")
                
                if "top_p" in new_params:
                    if not isinstance(new_params["top_p"], (int, float)) or \
                       not 0 <= new_params["top_p"] <= 1:
                        raise ValueError("top_p必须在0到1之间")
                
                config.model_info["parameters"].update(new_params)
                logger.debug(f"已更新模型 {model_name} 的参数设置")
            
            # 如果需要，重新创建会话
            if session_needs_update:
                logger.info(f"配置变更需要重新创建模型 {model_name} 的会话")
                if model_name in self.sessions:
                    session = self.sessions[model_name]
                    if not session.closed:
                        await session.close()
                    self.sessions.pop(model_name)
                    logger.debug(f"已关闭模型 {model_name} 的旧会话")
            
            logger.info(f"模型 {model_name} 配置更新完成")
            
        except (KeyError, ValueError) as e:
            logger.error(f"更新模型 {model_name} 配置失败: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"更新模型 {model_name} 配置时发生错误: {str(e)}")
            raise Exception(f"更新配置失败: {str(e)}")
    
    async def generate_completion(
        self,
        model_name: str,
        messages: List[Dict[str, str]],
        stream: bool = False
    ) -> Union[str, AsyncGenerator[str, None]]:
        """
        生成模型响应
        
        Args:
            model_name: 模型名称
            messages: 消息列表
            stream: 是否使用流式输出
            
        Returns:
            Union[str, AsyncGenerator[str, None]]: 模型响应。
            如果stream=True，返回一个异步生成器；否则返回字符串。
            
        Raises:
            KeyError: 当模型不存在时
            ValueError: 当消息格式无效时
            Exception: 当API调用失败时
        """
        # 验证模型是否存在
        if model_name not in self.models:
            raise KeyError(f"模型不存在#2: {model_name}")
            
        # 验证消息格式
        if not messages or not isinstance(messages, list):
            raise ValueError("消息列表不能为空且必须是列表类型")
        
        for msg in messages:
            if not isinstance(msg, dict) or 'role' not in msg or 'content' not in msg:
                raise ValueError("消息格式无效，必须包含'role'和'content'字段")
        
        config = self.models[model_name]
        logger.info(f"使用模型 {model_name} 生成响应")        # 确保session存在且有效
        if model_name not in self.sessions or self.sessions[model_name].closed:
            logger.info(f"初始化模型 {model_name} 的会话")
            # 如果session存在但已关闭，先移除它
            if model_name in self.sessions:
                del self.sessions[model_name]
            # 重新初始化这个模型的会话
            try:
                await self._initialize_model_session(model_name, config)
            except Exception as e:
                logger.error(f"初始化模型 {model_name} 会话失败: {str(e)}")
                raise
        
        session = self.sessions[model_name]
        if session.closed:
            logger.error(f"模型 {model_name} 的会话已关闭")
            raise Exception(f"模型 {model_name} 的会话已关闭")
        # 针对qwen适配dashscope API
        if model_name == "qwen":
            api_path = "/api/v1/services/aigc/text-generation/generation"
            request_data = {
                "model": config.model_info["name"],
                "input": {
                    "messages": messages
                },
                "parameters": {
                    "temperature": config.model_info["parameters"]["temperature"],
                    "top_p": config.model_info["parameters"]["top_p"]
                }
            }
        else:
            api_path = "/chat/completions"
            request_data = {
                "model": config.model_info["name"],
                "messages": messages,
                "stream": stream,
                **config.model_info["parameters"]
            }
        try:
            logger.debug(f"发送请求到模型 {model_name}")
            logger.debug(f"request_data请求数据: {request_data['model'],request_data.get('stream', '没有stream参数')}")
            async with session.post(api_path, json=request_data) as response:
                if response.status != 200:
                    error_text = await response.text()
                    logger.error(f"API调用失败 ({response.status}): {error_text}")
                    raise Exception(f"API调用失败 ({response.status}): {error_text}")
                if stream:
                    logger.debug(f"开始流式响应处理")
                    return self._handle_stream_response(response)
                else:
                    logger.debug(f"开始普通响应处理")
                    logger.debug(f"成功解析API响应内容，开始对{model_name}模型的响应内容处理:\n{response}")
                    return await self._handle_normal_response(response, model_name)
        except aiohttp.ClientError as e:
            logger.error(f"网络请求失败: {str(e)}")
            raise Exception(f"网络请求失败: {str(e)}")
        except Exception as e:
            logger.error(f"模型调用失败: {str(e)}")
            raise Exception(f"模型调用失败: {str(e)}")
    
    async def _handle_normal_response(self, response: aiohttp.ClientResponse, model_name: str = None) -> str:
        """
        处理普通响应
        
        Args:
            response: aiohttp响应对象
            
        Returns:
            str: 模型响应内容
            
        Raises:
            Exception: 当API调用失败或响应格式无效时
        """
        try:
            data = await response.json()
            logger.debug(f"成功解析API响应内容，开始处理:\n{data}")
            # 针对qwen（dashscope）API返回格式
            if model_name == "qwen":
                if "output" in data and "text" in data["output"]:
                    return data["output"]["text"]
                else:
                    raise ValueError("qwen响应格式无效：缺少output.text字段")
            # OpenAI格式
            if not isinstance(data, dict):
                raise ValueError("响应格式无效：不是JSON对象")
            if "choices" not in data or not data["choices"]:
                raise ValueError("响应格式无效：缺少choices字段或为空")
            if "message" not in data["choices"][0]:
                raise ValueError("响应格式无效：缺少message字段")
            if "content" not in data["choices"][0]["message"]:
                raise ValueError("响应格式无效：缺少content字段")
            content = data["choices"][0]["message"]["content"]
            logger.debug(f"成功提取响应内容，长度: {len(content)}")
            logger.debug(f"模型 {model_name} 响应: {content}")
            return content
        except aiohttp.ContentTypeError as e:
            error_text = await response.text()
            logger.error(f"响应解析失败: {str(e)}, 原始响应: {error_text}")
            raise Exception(f"响应解析失败: {str(e)}")
        except ValueError as e:
            logger.error(f"响应格式无效: {str(e)}")
            raise Exception(f"响应格式无效: {str(e)}")
        except Exception as e:
            logger.error(f"处理响应时发生错误: {str(e)}")
            raise Exception(f"处理响应时发生错误: {str(e)}")
    
    async def _handle_stream_response(
        self,
        response: aiohttp.ClientResponse
    ) -> AsyncGenerator[str, None]:
        """
        处理流式响应
        
        Args:
            response: aiohttp响应对象
            
        Yields:
            str: 流式响应的内容片段
            
        Raises:
            Exception: 当API调用失败时
        """
        if response.status != 200:
            error_text = await response.text()
            raise Exception(f"API调用失败 ({response.status}): {error_text}")
        
        try:
            async for line in response.content:
                if line:
                    try:
                        data = json.loads(line.decode())
                        if data["choices"][0]["finish_reason"] is not None:
                            break
                        content = data["choices"][0]["delta"].get("content")
                        if content:
                            yield content
                    except Exception as e:
                        logger.error(f"处理流式响应失败: {str(e)}")
                        continue
        except Exception as e:
            logger.error(f"读取流式响应失败: {str(e)}")
            raise
        finally:
            # 确保生成器正确终止
            if not response.closed:
                await response.release()

    async def _initialize_model_session(self, model_name: str, model_config: ModelConfig) -> None:
        """
        为单个模型初始化会话
        
        Args:
            model_name: 模型名称
            model_config: 模型配置
            
        Raises:
            ValueError: 当配置无效时
            Exception: 当创建会话失败时
        """
        try:
            # 验证配置
            if not model_config.base_url:
                raise ValueError(f"模型 {model_name} 缺少base_url")
            if not model_config.api_key:
                raise ValueError(f"模型 {model_name} 缺少api_key")
            
            # 创建会话
            self.sessions[model_name] = aiohttp.ClientSession(
                base_url=model_config.base_url,
                headers={
                    "Authorization": f"Bearer {model_config.api_key}",
                    "Content-Type": "application/json"
                }
            )
            logger.info(f"模型 {model_name} 会话创建成功")
            
        except ValueError as e:
            logger.error(f"模型 {model_name} 配置无效: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"为模型 {model_name} 创建会话失败: {str(e)}")
            raise Exception(f"创建会话失败: {str(e)}")


# 使用示例
async def main():
    """ModelManager使用示例"""
    # 创建模型管理器
    manager = ModelManager()
    logger.info("开始ModelManager使用示例")
    
    try:
        # 添加默认模型配置
        from .model_config import DEFAULT_MODELS
        for name, config in DEFAULT_MODELS.items():
            try:
                manager.add_model(name, config)
                logger.info(f"成功添加模型: {name}")
            except Exception as e:
                logger.error(f"添加模型 {name} 失败: {str(e)}")
                # 新增：添加失败时立即抛出异常
                raise RuntimeError(f"关键模型 {name} 配置失败，请检查配置") from e
        
        # 新增：验证模型是否实际添加成功
        if "deepseek" not in manager.models:
            raise KeyError("deepseek模型未正确加载，请检查配置")
        
        # 初始化模型
        try:
            await manager.initialize()
            logger.info("模型初始化成功")
        except Exception as e:
            logger.error(f"模型初始化失败: {str(e)}")
            raise
        
        # 示例1：基本使用
        logger.info("示例1：基本使用")
        messages = [
            {"role": "system", "content": "你是一个专业的AI助手。"},
            {"role": "user", "content": "你好，请介绍一下自己。"}
        ]
        
        try:
            response = await manager.generate_completion("deepseek", messages)
            print("\n基本响应示例:")
            print(response)
            logger.info("基本使用示例完成")
        except Exception as e:
            logger.error(f"基本使用示例失败: {str(e)}")
        
        # 示例2：流式输出
        logger.info("示例2：流式输出")
        try:
            print("\n流式响应示例:")
            async for chunk in await manager.generate_completion(
                "deepseek", messages, stream=True
            ):
                print(chunk, end="", flush=True)
            print("\n")
            logger.info("流式输出示例完成")
        except Exception as e:
            logger.error(f"流式输出示例失败: {str(e)}")
        
        # 示例3：配置更新
        logger.info("示例3：配置更新")
        try:
            await manager.update_model_config("deepseek", {
                "parameters": {
                    "temperature": 0.8,
                    "max_tokens": 2000
                }
            })
            logger.info("配置更新示例完成")
        except Exception as e:
            logger.error(f"配置更新示例失败: {str(e)}")
        
        # 示例4：错误处理
        logger.info("示例4：错误处理")
        try:
            # 尝试使用不存在的模型
            await manager.generate_completion("nonexistent_model", messages)
        except KeyError as e:
            print(f"\n预期的错误 - 模型不存在: {str(e)}")
            logger.info("错误处理示例完成")
        
    except Exception as e:
        logger.error(f"示例运行失败: {str(e)}")
        raise
    finally:
        # 关闭管理器
        try:
            await manager.close()
            logger.info("ModelManager已关闭")
        except Exception as e:
            logger.error(f"关闭ModelManager失败: {str(e)}")


if __name__ == "__main__":
    # 运行示例
    logger.info("运行ModelManager使用示例...")
    asyncio.run(main())
    logger.info("示例运行完成")