[build-system]
requires = ["setuptools>=45", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "codetools-testcase-generator"
version = "1.0.0"
description = "AI-powered test case generation and review system"
authors = [
    {name = "CodeTools Team"}
]
readme = "README.md"
requires-python = ">=3.8"
dependencies = [
    "streamlit>=1.44.1",
    "aiohttp>=3.8.0",
    "xlsxwriter>=3.0.0",
    "python-docx>=0.8.11",
    "python-dotenv>=1.0.0",
    "pyyaml>=6.0",
    "typing-extensions>=4.0.0",
    "pydantic>=2.0.0"
]

[project.optional-dependencies]
dev = [
    "mypy>=1.0.0",
    "black>=22.0.0",
    "isort>=5.0.0",
    "flake8>=4.0.0",
    "pytest>=7.0.0",
    "pytest-asyncio>=0.20.0"
]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["utils", "models", "ui", "tipwords", "download"]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py", "*_test.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short"
asyncio_mode = "auto"
