"""
主要作用是生成测试用例,并构建最终的测试结果报告
"""
from typing import List, Dict, Optional, Tuple, Any
import streamlit as st
import json
from models import model_manager, ModelConfig
from ui.input_utils import render_advanced_options
from utils.logger import setup_logger

# 设置日志（调用自定义logger）
logger = setup_logger()

async def generate_test_cases(
    user_input: str,
    system_message: str,
    test_priority: str,
    test_case_count: int,
    cases_rate_list: List[int],
    model_name: str  # 新增参数，指定生成模型
) -> List[str]:
    """
    生成测试用例
    
    Args:
        user_input: 用户输入的需求描述
        system_message: 系统消息
        test_priority: 测试优先级
        test_case_count: 测试用例数量
        cases_rate_list: 用例类型比例列表
        model_name: 指定生成模型的名称
    
    Returns:
        List[str]: 生成的测试用例列表
    """
    if not st.session_state.model_initialized:
        raise Exception("模型未初始化#3")
    
    # 准备消息
    messages = [
        {"role": "system", "content": system_message},
        {"role": "user", "content": f"""
需求描述：
{user_input}

测试要求：
1. 优先级：{test_priority}
2. 用例数量：{test_case_count}
3. 用例类型比例：
   - 功能用例：{cases_rate_list[0]}%
   - 边界用例：{cases_rate_list[1]}%
   - 异常用例：{cases_rate_list[2]}%
   - 性能/兼容性用例：{cases_rate_list[3]}%
   - 回归测试用例：{cases_rate_list[4]}%
"""}
    ]
    
    # 使用指定的模型生成响应
    try:
        logger.info(f"使用模型 {model_name} 生成测试用例，消息内容:{messages}")
        response = await model_manager.generate_completion(model_name, messages)
        
        # 解析响应
        try:
            cases = json.loads(response)
            if isinstance(cases, list):
                return [json.dumps(case, ensure_ascii=False, indent=2) for case in cases]
            else:
                return [response]
        except json.JSONDecodeError:
            return [response]
            
    except Exception as e:
        logger.error(f"生成测试用例失败: {str(e)}")
        raise e

def build_final_result(original_cases: str, review_results: List[str]) -> str:
    """
    功能描述： 将原始生成的测试用例与每轮评审结果拼接为一个完整的结构化文本。

    Args:
        original_cases: 原始测试用例字符串
        review_results: 多轮评审后的修改建议或反馈
        
    Returns:
        str: 整合后的完整结果字符串
    """
    parts = []
    parts.append("# 原始测试用例")
    parts.append(original_cases.strip())
    parts.append("")

    for idx, review_result in enumerate(review_results):
        parts.append(f"# 第{idx + 1}轮评审结果")
        parts.append(review_result.strip())
        if idx != len(review_results) - 1:
            parts.append("")  # 最后一轮不加空行

    return "\n\n".join(parts).strip()

# 废弃的函数，保留为兼容性考虑



