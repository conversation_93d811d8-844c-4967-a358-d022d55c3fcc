"""
下载管理模块，负责处理文件下载相关的功能。
"""

import os
import json
from typing import List, Dict, Optional, Union
import xlsxwriter
from datetime import datetime

from models.result_parser import ResultParser
from .result_persistence import save_result_to_json
from utils.logger import setup_logger


# 设置日志记录器
logger = setup_logger()


class DownloadManager:
    """下载管理器类"""
    
    def __init__(self):
        pass
    
    def _get_timestamp(self) -> str:
        """获取时间戳字符串"""
        return datetime.now().strftime("%Y%m%d_%H%M%S")
    
    def _sanitize_filename(self, filename: str) -> str:
        """
        清理文件名，移除非法字符
        
        Args:
            filename: 原始文件名
            
        Returns:
            str: 清理后的文件名
        """
        # 移除非法字符
        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            filename = filename.replace(char, '_')
        return filename
    
    def generate_excel_multisheet(
        self,
        generator_cases: list,
        reviewer_cases: Optional[list] = None,
        filename: str = "testcases.xlsx",
        output_dir: Optional[str] = None
    ) -> str:
        """
        生成多sheet的excel文件
        
        Args:
            generator_cases: 生成器生成的测试用例
            reviewer_cases: 评审器评审结果(可选)
            filename: 输出文件名
            output_dir: 输出目录
        
        Returns:
            str: 生成的文件路径
        """
        if not output_dir:
            output_dir = os.path.join("logs", self._get_timestamp())
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        workbook = xlsxwriter.Workbook(filepath)

        # Sheet 1: 生成的测试用例
        worksheet1 = workbook.add_worksheet("Generate_test_cases")
        content1 = "\n".join(generator_cases)
        self._write_table_to_worksheet(worksheet1, content1)

        # Sheet 2: 评审结果（如果有）
        if reviewer_cases:
            worksheet2 = workbook.add_worksheet("Review_test_cases")
            content2 = "\n".join(reviewer_cases)
            self._write_table_to_worksheet(worksheet2, content2)

        workbook.close()
        logger.info(f"Excel文件已生成: {filepath}")
        return filepath
    
    def _write_table_to_worksheet(self, worksheet, content: str) -> None:
        """
        将表格内容写入worksheet
        
        Args:
            worksheet: Excel工作表对象
            content: markdown格式的表格内容
        """
        # 提取表格内容
        table_lines = []
        for line in content.splitlines():
            stripped_line = line.strip()
            if stripped_line.startswith("|"):
                if set(stripped_line.replace("|", "").strip()) != {"-"}:  # 跳过分隔线
                    table_lines.append(line)
                
        # 写入数据
        for row_idx, line in enumerate(table_lines):
            cells = [cell.strip().replace("<br>", "\n") for cell in line.split("|")[1:-1]]
            for col_idx, cell in enumerate(cells):
                worksheet.write(row_idx, col_idx, cell)
    
    def generate_markdown_multisource(
        self,
        generator_cases: list,
        reviewer_cases: Optional[list] = None,
        filename: str = "testcases.md",
        output_dir: Optional[str] = None
    ) -> str:
        """
        生成多来源的markdown文件
        
        Args:
            generator_cases: 生成器生成的测试用例
            reviewer_cases: 评审器评审结果(可选)
            filename: 输出文件名
            output_dir: 输出目录
            
        Returns:
            str: 生成的文件路径
        """
        if not output_dir:
            output_dir = os.path.join("logs", self._get_timestamp())
        os.makedirs(output_dir, exist_ok=True)
        
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("# Generate_test_cases\n\n")
            for case in generator_cases:
                md = ResultParser.extract_latest_markdown(case)
                f.write(md + "\n\n")
            if reviewer_cases:
                f.write("# Review_test_cases\n\n")
                for case in reviewer_cases:
                    md = ResultParser.extract_latest_markdown(case)
                    f.write(md + "\n\n")
        
        logger.info(f"Markdown文件已生成: {filepath}")
        return filepath
    
    def save_and_generate_all_files(
        self,
        generator_responses: list,
        reviewer_responses: Optional[list],
        output_dir: str,
        case_list: Optional[list],
        result_data: dict
    ) -> None:
        """
        保存并生成所有格式的结果文件
        
        Args:
            generator_responses: 生成器响应列表
            reviewer_responses: 评审器响应列表(可选)
            output_dir: 输出目录
            case_list: 测试用例列表(可选)
            result_data: 结果元数据
        """
        try:
            # 保存JSON格式的元数据
            save_result_to_json({
                "generator_responses": generator_responses,
                "reviewer_responses": reviewer_responses,
                "case_list": case_list,
                "result": result_data,
            }, output_dir)
            
            # 生成其他格式文件
            self.generate_markdown_multisource(
                generator_responses,
                reviewer_responses,
                output_dir=output_dir
            )
            self.generate_excel_multisheet(
                generator_responses,
                reviewer_responses,
                output_dir=output_dir
            )
            
        except Exception as e:
            logger.error(f"生成文件失败: {str(e)}")
            raise