"""
启动检查模块
在应用启动时检查必要的配置，并提供友好的设置向导
"""

import streamlit as st
from pathlib import Path
from typing import Dict, List, Tuple
from .constants import model_constants
import logging

logger = logging.getLogger(__name__)


class StartupChecker:
    """启动检查器"""
    
    def __init__(self):
        self.missing_keys: List[str] = []
        self.warnings: List[str] = []

    def _is_valid_api_key(self, api_key: str) -> bool:
        """
        检查API密钥是否有效（不是占位符）

        Args:
            api_key: API密钥字符串

        Returns:
            bool: 是否为有效的API密钥
        """
        if not api_key or not api_key.strip():
            return False

        api_key = api_key.strip()

        # 检查是否为占位符文本
        placeholder_patterns = [
            "your_",
            "sk-xxxxxxx",
            "sk-xxxx",
            "placeholder",
            "example",
            "test",
            "demo",
            "fake"
        ]

        # 检查是否为重复字符的占位符（如 sk-xxxxxxxxxxxxx）
        if api_key.startswith('sk-'):
            key_part = api_key[3:]  # 去掉 sk- 前缀
            unique_chars = len(set(key_part))
            if len(key_part) > 0 and unique_chars <= 3:  # 如果只有1-3种不同字符，很可能是占位符
                return False

        api_key_lower = api_key.lower()
        for pattern in placeholder_patterns:
            if pattern in api_key_lower:
                return False

        # 检查是否为明显的测试密钥（长度太短或格式不对）
        if len(api_key) < 20:  # 真实的API密钥通常比较长
            return False

        # 如果以sk-开头，检查后面是否有足够的字符
        if api_key.startswith('sk-') and len(api_key) < 30:
            return False

        return True

    def check_api_keys(self) -> Tuple[bool, Dict[str, bool]]:
        """
        检查API密钥配置

        Returns:
            Tuple[bool, Dict[str, bool]]: (是否有任何密钥配置, 各模型密钥状态)
        """
        key_status = {}
        has_any_key = False
        self.missing_keys = []  # 清空缺失密钥列表

        for model_name in model_constants.AVAILABLE_MODELS:
            # 只检查配置文件
            config_api_key = None
            try:
                from .config_manager import ConfigManager
                config_manager = ConfigManager()
                model_config = config_manager.get_model_config(model_name)
                if model_config:
                    config_api_key = model_config.api_key
            except Exception:
                pass

            # 检查是否有有效的API密钥
            config_valid = self._is_valid_api_key(config_api_key)

            key_status[model_name] = config_valid

            if config_valid:
                has_any_key = True
            else:
                self.missing_keys.append(model_name)

        return has_any_key, key_status
    

    
    def show_api_key_setup_guide(self):
        """显示API密钥设置向导"""
        st.error("🔑 API密钥配置缺失")

        # 首先检查配置文件中是否已有密钥
        has_config_keys = self._check_config_file_keys()
        if has_config_keys:
            st.info("✅ 检测到配置文件中已有API密钥，应用程序应该可以正常运行。")
            return
        
        st.markdown("""
        ### 📋 配置步骤

        编辑 `settings/config.ini` 文件：
        ```ini
        [deepseek]
        api_key = your_deepseek_api_key_here

        [qwen]
        api_key = your_qwen_api_key_here
        ```

        ### 🔗 获取API密钥

        - **DeepSeek**: [https://platform.deepseek.com/](https://platform.deepseek.com/)
        - **Qwen**: [https://dashscope.aliyuncs.com/](https://dashscope.aliyuncs.com/)
        """)
        
        st.warning("⚠️ 配置完成后，请重启应用程序以加载新的API密钥。")
    
    def show_partial_config_warning(self, key_status: Dict[str, bool]):
        """显示部分配置警告"""
        configured_models = [model for model, has_key in key_status.items() if has_key]
        missing_models = [model for model, has_key in key_status.items() if not has_key]

        st.warning(f"⚠️ 部分模型API密钥未配置")

        col1, col2 = st.columns(2)

        with col1:
            st.markdown("**✅ 已配置的模型:**")
            for model in configured_models:
                st.markdown(f"- {model}")

        with col2:
            st.markdown("**❌ 未配置的模型:**")
            for model in missing_models:
                st.markdown(f"- {model}")

        st.info("💡 你可以继续使用已配置的模型，或按照上述指南配置其他模型的API密钥。")
    
    def run_startup_check(self, show_success_message: bool = False) -> bool:
        """
        运行启动检查

        Args:
            show_success_message: 是否显示成功配置的提示信息

        Returns:
            bool: 是否可以继续运行应用
        """
        has_any_key, key_status = self.check_api_keys()

        if not has_any_key:
            # 没有任何API密钥配置
            self.show_api_key_setup_guide()
            return False
        elif len(self.missing_keys) > 0:
            # 部分API密钥缺失
            self.show_partial_config_warning(key_status)
            return True
        else:
            # 所有API密钥都已配置 - 只在指定时显示成功提示
            if show_success_message:
                st.success("✅ 所有API密钥已正确配置")

            return True
    
    def check_and_show_warnings(self):
        """检查并显示警告信息"""
        # 检查配置文件中是否还有硬编码的密钥
        self._check_hardcoded_keys()
    
    def _check_config_file_keys(self) -> bool:
        """检查配置文件中是否有API密钥"""
        try:
            from .config_manager import ConfigManager
            config_manager = ConfigManager()

            for model_name in model_constants.AVAILABLE_MODELS:
                model_config = config_manager.get_model_config(model_name)
                if model_config and model_config.api_key and model_config.api_key.strip():
                    return True
            return False
        except Exception:
            return False

    def _check_hardcoded_keys(self):
        """检查配置文件中是否有硬编码的API密钥"""
        # 这个方法现在不需要做任何事情，因为我们只使用配置文件
        pass


# 创建全局实例
startup_checker = StartupChecker()

__all__ = ['StartupChecker', 'startup_checker']
