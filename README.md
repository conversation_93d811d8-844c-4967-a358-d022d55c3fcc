# CodeTools 智能测试用例生成系统

## 项目概述
CodeTools 是基于大语言模型的测试用例自动化生成与评审系统，提供端到端的测试流程解决方案。系统采用模块化设计，支持多模型接入和扩展。

## 核心功能架构

```
# CodeTools 项目说明

## 项目简介

CodeTools 是一个面向测试用例自动生成与评审的智能工具，集成了用例生成、用例评审、日志管理、模型调用等多种功能，适用于软件测试流程自动化和智能化。

## 主要功能

- 根据需求文档自动生成高质量测试用例
- 对测试用例进行自动化评审与补充
- 支持用例日志、结果导出（Markdown/Excel/Word）
- 支持多种大模型接口调用
- 统一的提示词（Prompt）管理与自定义
- 便捷的UI界面组件支持

### 文件功能说明

#### 根目录文件

- `run.py`: 项目主入口，负责启动Streamlit应用并配置环境。
- `README.md`: 项目说明文件，包含项目简介、功能说明、快速开始指南等。

#### `download/` 目录

- `download_manager.py`: 下载管理模块，处理各种格式的文件下载逻辑。

#### `img/` 目录

- 项目图片资源，包括图标、二维码等。

#### `logs/` 目录

- 日志与用例生成、评审结果存档，支持多种格式导出。

#### `models/` 目录

- `model_utils.py`: 大模型相关工具与配置，负责初始化和管理多个大模型的配置与实例。
- `generate_utils.py`: 测试用例生成工具，包含生成测试用例的核心逻辑。
- `result_parser.py`: 结果解析器，用于解析模型生成的测试用例结果。

#### `settings/` 目录

- `config.ini`: 模型配置文件，包含API密钥、端点等配置信息。
- `model_config.ini`: 模型管理配置，定义可用模型和默认设置。
- `testcaseconfig.ini`: 测试用例生成的业务参数配置。

#### `tipwords/` 目录

- `prompt_word_template.py`: 提示词模板与系统消息管理，集中管理所有系统消息模板与提示词。

#### `ui/` 目录

- `components.py`: 可复用UI组件，提供多种UI组件如文件上传、提示词模板展示等。
- `styles.py`: CSS样式注入，负责前端页面的样式管理。
- `layout.py`: UI布局模块，负责管理应用程序的页面布局。

#### `utils/` 目录

- `logger.py`: 日志配置模块，负责日志的初始化与配置。
- `config_manager.py`: 配置文件管理，负责读取和保存配置文件。
- `message_utils.py`: 消息工具模块，负责加载系统消息。

## 目录结构说明

- `download/`         下载与结果持久化相关模块
- `img/`              项目图片资源
- `logs/`             日志与用例生成、评审结果存档
- `models/`           大模型相关工具与配置
- `settings/`         配置文件目录
- `tipwords/`         提示词模板与系统消息管理
- `ui/`               前端UI组件与布局
- `utils/`            工具类与通用方法
- `run.py`            项目主入口
- `README.md`         项目说明文件

## 主要功能模块说明

根目录文件
文件名	功能作用
run.py	项目主入口文件，启动应用的核心逻辑。
page.py	Streamlit 页面逻辑控制文件，包含页面初始化、样式加载及配置读取等功能。
config.ini	应用全局配置文件，包含模型参数、API 地址等基础设置。
README.md	项目说明文档，介绍项目背景、使用方式、目录结构等信息。
目录结构说明

download/
文件名	功能作用
download_manager.py	下载管理器类，负责生成并导出测试用例为 Markdown、Excel、JSON 等格式。
result_persistence.py	持久化保存测试用例生成结果及相关响应数据，便于后续查阅或下载。


img/
文件名	功能作用
Jack.png, favicon.ico, gzh.png	项目使用的图片资源，包括侧边栏图标、二维码等 UI 元素。

logs/
文件名	功能作用
子目录（如 20250503_201013）	每次生成测试用例的输出结果存档目录，包含 .md、.xlsx、.json 和 .docx 格式的结果文件。
日志文件（如 app-2025-05-06.log）	运行时日志记录文件，用于调试和监控系统行为。

models/
文件名	功能作用
init.py	Python 包初始化文件，声明该目录为模块包。
generate_utils.py	测试用例生成工具函数，封装 AI 模型调用与结果构建逻辑。
model_config.py	定义 ModelConfig 类，用于统一管理模型配置项。
model_manager.py	模型管理器，负责添加、移除和异步初始化多个模型实例。
model_utils.py	模型初始化工具函数，用于从配置中动态加载模型并校验其合法性。
result_parser.py	结果解析工具类，提取并格式化模型返回内容中的关键数据。

settings/
文件名	功能作用
config.ini	模型API配置文件，包含各模型的API密钥、端点等信息。
model_config.ini	模型管理配置，定义可用模型列表和默认角色分配。
testcaseconfig.ini	测试用例专用配置文件，用于设定测试类型、优先级等业务参数。

tipwords/
文件名	功能作用
review_case_prompt_words/TESTCASE_READER_SYSTEM_MESSAGE.txt	评审测试用例时使用的系统消息模板，指导模型行为。
test_case_prompt_words/TESTCASE_WRITER_SYSTEM_MESSAGE.txt	生成测试用例时使用的系统消息模板，指导模型行为。
prompt_word_template.py	提示词模板路径管理文件，集中维护各模板的引用路径。

ui/
components.py
函数名	功能作用
render_file_upload()	渲染上传需求文件组件，允许用户导入 .txt 文件作为输入。
render_message_templates()	渲染提示词模板展示区域，支持编辑编写与评审提示词。
render_download_buttons()	渲染下载按钮组件，支持导出 .md、.xlsx 等格式的测试用例。
render_reviewer_result()	渲染评审模型输出结果，展示多轮反馈。
render_history_data_view()	展示历史生成的测试用例与评审结果，并提供下载功能。
layout.py
函数名	功能作用
setup_page_config()	设置页面基本配置，如标题、图标、布局等。
render_header()	渲染页面头部信息，包括标题、副标题和公众号二维码。
render_sidebar()	渲染侧边栏，显示模型状态、配置信息及帮助链接。
session_utils.py | （假设存在）
函数名	功能作用
reset_session_state()	重置会话状态变量，用于清除缓存数据。
styles.py
函数名	功能作用
apply_base_styles()	应用基础 CSS 样式，统一页面视觉风格。
get_404_styles()	返回 404 页面的样式定义。

utils/
文件名	功能作用
config_manager.py	配置管理器类，负责读取、更新和保存 config.ini 中的配置。
logger.py	日志工具模块，配置结构化日志输出格式，支持 JSON 与控制台双输出。
message_utils.py	消息处理工具类，用于构造系统消息、处理异常信息等。
## 快速开始

### 手动设置

#### 1. 安装依赖
```bash
# 建议使用虚拟环境
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 安装依赖
pip install -r requirements.txt
```

#### 2. 配置API密钥

**手动配置**
```bash
# 创建 .env 文件
touch .env

# 编辑 .env 文件，填入你的API密钥
```

编辑 `.env` 文件内容：
```env
# DeepSeek API密钥（从 https://platform.deepseek.com/ 获取）
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Qwen API密钥（从 https://dashscope.aliyuncs.com/ 获取）
QWEN_API_KEY=your_qwen_api_key_here

# 日志配置
LOG_LEVEL=INFO
LOG_DIR=logs

# Streamlit配置
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=localhost
```

**验证配置：**
启动应用后，系统会自动检查API密钥配置状态。

#### 3. 运行应用
支持多种启动方式：
```bash
# 方式1：使用启动脚本（推荐）
python run.py

# 方式2：直接运行streamlit
streamlit run page.py

# 方式3：PowerShell环境
python -m streamlit run .\page.py
```

#### 4. 验证安装
启动应用后，系统会自动进行配置检查和状态验证。

### 方式三：Docker部署

#### 使用Docker Compose（推荐）
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

#### 使用Docker
```bash
# 构建镜像
docker build -t testcase-generator .

# 运行容器
docker run -d \
  --name testcase-generator \
  -p 8501:8501 \
  -v $(pwd)/logs:/app/logs \
  -e DEEPSEEK_API_KEY=your_key \
  -e QWEN_API_KEY=your_key \
  testcase-generator
```

### 配置说明
- 主要配置文件位于 `settings/` 目录
- API密钥优先从环境变量读取，确保安全性
- 可根据需要调整 `settings/testcaseconfig.ini` 中的业务参数
- 日志文件保存在 `logs/` 目录下

提示词管理
所有系统消息模板与提示词均在 tipwords/ 目录下统一维护，通过 prompt_word_template.py 集中管理路径。

日志与结果
每次用例生成与评审的结果会自动保存在 logs/ 目录下，支持多种格式导出。

## 故障排除

### 常见问题

#### 1. API密钥相关
```bash
# 问题：API调用失败
# 解决：检查 .env 文件中的API密钥配置
# 确保API密钥格式正确且有效
```

#### 2. 依赖安装问题
```bash
# 问题：某些包安装失败
# 解决：升级pip并重新安装
pip install --upgrade pip
pip install -r requirements.txt --force-reinstall
```

#### 3. 端口占用
```bash
# 问题：8501端口被占用
# 解决：使用其他端口
streamlit run page.py --server.port 8502
```

#### 4. Docker相关
```bash
# 问题：容器启动失败
# 解决：检查日志
docker-compose logs testcase-generator

# 重新构建镜像
docker-compose build --no-cache
```

### 性能优化建议

1. **内存使用**：建议至少分配1GB内存
2. **并发处理**：避免同时处理多个大型文档
3. **日志管理**：定期清理日志文件
4. **API限制**：注意API调用频率限制

## 开发指南

### 代码质量
```bash
# 格式化代码（如果安装了black）
black .

# 类型检查（如果安装了mypy）
mypy --ignore-missing-imports .

# 语法检查
python -m py_compile page.py run.py
```

### 测试
```bash
# 运行测试
pytest tests/

# 运行特定测试
pytest tests/test_config_manager.py -v
```

### 贡献指南
1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 运行测试和质量检查
5. 提交 Pull Request

## 适用场景
- 软件测试团队自动化用例设计
- 测试用例评审与补充
- 测试流程智能化升级
- 质量保证流程优化

## 版本历史
- **v2025.06.28**: 重大优化版本
  - 安全性增强：移除硬编码API密钥
  - 性能优化：改进异步处理和日志管理
  - 代码质量：添加类型提示和错误处理
  - 部署优化：改进Docker配置
  - 测试框架：添加自动化测试

## 许可证
本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系方式
如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件至项目维护者
- 查看项目文档获取更多帮助

---
✅ **优化完成**：此版本已全面优化项目架构、安全性、性能和可维护性。
