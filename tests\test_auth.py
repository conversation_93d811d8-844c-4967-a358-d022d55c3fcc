#!/usr/bin/env python3
"""
测试认证和用户管理功能的脚本
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.auth_manager import AuthManager
from utils.logger import setup_logger

logger = setup_logger()

def test_auth_manager():
    """测试认证管理器功能"""
    print("=== 测试认证管理器功能 ===")
    
    try:
        # 创建认证管理器实例
        auth_manager = AuthManager()
        print("✅ 认证管理器初始化成功")
        
        # 测试获取用户信息
        user_info = auth_manager._get_user_info("admin")
        print(f"✅ 获取admin用户信息: {user_info}")
        
        # 模拟session state
        import streamlit as st
        if not hasattr(st, 'session_state'):
            class MockSessionState:
                def __init__(self):
                    self.data = {}
                
                def get(self, key, default=None):
                    return self.data.get(key, default)
                
                def __setattr__(self, key, value):
                    if key == 'data':
                        super().__setattr__(key, value)
                    else:
                        self.data[key] = value
                
                def __getattr__(self, key):
                    return self.data.get(key)
            
            st.session_state = MockSessionState()
        
        # 设置模拟的session state
        st.session_state.user_info = user_info
        
        # 测试管理员检查
        is_admin = auth_manager.is_admin()
        print(f"✅ 管理员检查: {is_admin}")
        
        # 测试获取所有用户
        all_users = auth_manager.get_all_users()
        print(f"✅ 获取所有用户: {len(all_users)} 个用户")
        for user in all_users:
            print(f"   - {user['username']} ({user['name']}) - {user['role']}")
        
        # 测试添加用户
        print("\n--- 测试添加用户 ---")
        success = auth_manager.add_user(
            username="testuser",
            name="测试用户",
            email="<EMAIL>",
            password="test123",
            role="user"
        )
        print(f"✅ 添加用户结果: {success}")
        
        if success:
            # 再次获取所有用户
            all_users = auth_manager.get_all_users()
            print(f"✅ 添加后用户数量: {len(all_users)} 个用户")
            
            # 测试删除用户
            print("\n--- 测试删除用户 ---")
            delete_success = auth_manager.delete_user("testuser")
            print(f"✅ 删除用户结果: {delete_success}")
            
            if delete_success:
                all_users = auth_manager.get_all_users()
                print(f"✅ 删除后用户数量: {len(all_users)} 个用户")
        
        print("\n🎉 所有测试通过！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_auth_manager()
