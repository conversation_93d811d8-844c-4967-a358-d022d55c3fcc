"""
主要作用是 初始化和管理多个大模型的配置与实例，确保它们在应用启动时能够正确加载并准备好供后续调用。
功能描述： 根据传入的配置信息（ConfigManager），动态加载并初始化多个AI模型，供后续如生成测试用例等功能使用。

关键流程：

清除已存在的模型实例，避免重复加载。
从配置中读取所有模型名称，并逐个处理。
对每个模型进行配置校验（是否启用、必填字段是否存在等）。
将合法模型通过 model_manager.add_model 添加到模型管理器中。
调用 model_manager.initialize() 完成异步初始化。
设置 st.session_state.model_initialized 状态标志模型是否初始化成功。
"""

import streamlit as st
# 导入自定义模块
from utils.config_manager import ConfigManager
from utils.logger import setup_logger
from models import model_manager, ModelConfig

# 设置日志（调用自定义logger）
logger = setup_logger()


async def initialize_models(config_manager) -> None:
    """
    初始化模型配置
    
    Args:
        config_manager: 配置管理器实例
        
    Raises:
        Exception: 当初始化失败时抛出
    """
    try:
        logger.info("开始初始化模型配置")
        
        # 清除现有模型
        if hasattr(model_manager, 'models'):
            for model_name in list(model_manager.models.keys()):
                try:
                    model_manager.remove_model(model_name)
                    logger.debug(f"已移除模型: {model_name}")
                except Exception as e:
                    logger.warning(f"移除模型 {model_name} 失败: {str(e)}")
        
        # 获取所有模型配置
        model_names = config_manager.get_all_models()
        if not model_names:
            logger.warning("配置中没有找到任何模型")
            return
            
        logger.info(f"找到 {len(model_names)} 个模型配置")        # 添加新的模型配置
        success_count = 0
        loaded_models = []
        logger.info(f"开始处理模型初始化，找到以下模型: {model_names}")
        
        for model_name in model_names:
            try:
                # 获取模型配置
                logger.info(f"正在获取模型 {model_name} 的配置...")
                model_config = config_manager.get_model_config(model_name)
                
                # 打印模型配置详细信息，隐藏api_key的具体值
                logger.info(
                    f"处理模型[{model_name}] 配置:\n"
                    f"- choice: {getattr(model_config, 'choice', None)}\n"
                    f"- name: {getattr(model_config, 'name', None)}\n"
                    f"- api_key: {'已设置' if getattr(model_config, 'api_key', None) else '未设置'}\n"
                    f"- base_url: {getattr(model_config, 'base_url', None)}\n"
                    f"- tokens: {getattr(model_config, 'tokens', None)}"
                )
                
                if not model_config:
                    logger.warning(f"无法获取模型 {model_name} 的配置，跳过")
                    continue
                
                if not getattr(model_config, 'choice', False):
                    logger.warning(f"模型 {model_name} 未启用(choice=False)，跳过")
                    continue
                  # 验证配置数据
                if not getattr(model_config, 'choice', False):
                    logger.warning(f"模型 {model_name} 未启用(choice=False)，跳过")
                    continue
                
                if not all([getattr(model_config, 'name', None), getattr(model_config, 'base_url', None), getattr(model_config, 'api_key', None)]):
                    logger.warning(f"模型 {model_name} 配置不完整，跳过")
                    continue
                
                # 验证配置对象类型
                if not isinstance(model_config, ModelConfig):
                    error_msg = f"无效的配置对象类型: {type(model_config)}"
                    logger.error(error_msg)
                    raise ValueError(error_msg)
                
                # 验证配置数据
                try:
                    model_config.validate()
                    logger.debug(f"模型 {model_name} 配置验证通过")
                except ValueError as e:
                    error_msg = f"模型 {model_name} 配置验证失败: {str(e)}"
                    logger.error(error_msg)
                    continue
                
                # 添加模型
                try:
                    model_manager.add_model(model_name, model_config)
                    loaded_models.append(model_name)
                    success_count += 1
                    logger.info(f"成功添加模型: {model_name}")
                except Exception as e:
                    logger.error(f"添加模型 {model_name} 失败: {str(e)}")
                    continue
            
            except Exception as e:
                logger.error(f"处理模型 {model_name} 时出错: {str(e)}", exc_info=True)
                continue

        if success_count == 0:
            error_msg = "没有成功添加任何模型配置，请检查配置文件和choice字段、必填字段是否正确！"
            logger.error(error_msg)
            raise Exception(error_msg)
            
        logger.info(f"成功加载的模型列表: {loaded_models}")
        
        # 初始化模型会话
        try:
            logger.info("正在初始化模型会话...")
            await model_manager.initialize()
            st.session_state.model_initialized = True
            logger.info(f"模型初始化完成，共成功加载 {success_count} 个模型")
        except Exception as init_error:
            error_msg = f"初始化模型会话失败: {str(init_error)}"
            logger.error(error_msg)
            st.session_state.model_initialized = False
            raise Exception(error_msg)
        
    except Exception as e:
        error_msg = f"模型初始化失败: {str(e)}"
        logger.error(error_msg, exc_info=True)
        st.session_state.model_initialized = False
        raise Exception(error_msg)
