import streamlit as st
from utils.config_manager import Config<PERSON>anager
from download.download_manager import DownloadManager

def initialize_session_state(defaults: dict = None):
    """
    初始化Streamlit的session_state。
    Args:
        defaults: 默认的session_state字典
    """
    if defaults is None:
        defaults = {}

    default_states = {
        'config_manager': ConfigManager(),
        'download_manager': DownloadManager(),
        'case_list': [],
        'result': "",
        'model_initialized': False
    }

    # 更新默认值
    default_states.update(defaults)

    # 初始化session state（除了role_state）
    for key, value in default_states.items():
        if key not in st.session_state:
            st.session_state[key] = value

    # role_state 需要特殊处理，因为它可能需要从URL恢复
    if 'role_state' not in st.session_state:
        # 首先尝试从URL参数恢复
        role_state = _restore_role_state_from_url()
        if not role_state:
            # 如果URL中没有状态，使用默认值
            role_state = {
                'generator_model': None,
                'reviewer_model': None,
                'role_type': 'none'
            }
        st.session_state.role_state = role_state

def _restore_role_state_from_url() -> dict:
    """从URL参数恢复角色状态"""
    try:
        query_params = st.query_params
        generator = query_params.get("generator", None)
        reviewer = query_params.get("reviewer", None)
        role_type = query_params.get("role_type", None)

        if generator or reviewer or role_type:
            role_state = {}
            if generator and generator != "None":
                role_state['generator_model'] = generator
            if reviewer and reviewer != "None":
                role_state['reviewer_model'] = reviewer
            if role_type and role_type != "none":
                role_state['role_type'] = role_type

            # 确保所有必需的键都存在
            role_state.setdefault('generator_model', None)
            role_state.setdefault('reviewer_model', None)
            role_state.setdefault('role_type', 'none')

            return role_state
    except Exception:
        pass

    return None
