"""
UI样式模块，负责管理应用程序的CSS样式。
"""

from typing import Dict

# 基础样式
BASE_STYLES = {
    'text_ellipsis': '''
    .edw49t12 {
        max-width: 500px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
    ''',
    
    'footer_remove': '''
    $(document).ready(function(){
        $("footer", window.parent.document).remove()
    });
    ''',
    
    '404_background': '''
    .css-fg4pbf{
        background-image: url('https://img.zcool.cn/community/0156cb59439764a8012193a324fdaa.gif');
        background-size: 100% 100%;
        background-attachment: fixed;
    }
    '''
}

def get_style(style_name: str) -> str:
    """
    获取指定名称的样式

    Args:
        style_name: 样式名称

    Returns:
        str: 样式内容
    """
    return BASE_STYLES.get(style_name, '')

def apply_base_styles() -> str:
    """
    应用基础样式

    Returns:
        str: 合并后的样式内容
    """
    return BASE_STYLES['text_ellipsis']

def get_404_styles() -> Dict[str, str]:
    """
    获取404页面的样式

    Returns:
        Dict[str, str]: 包含背景样式和文本样式的字典
    """
    return {
        'background': BASE_STYLES['404_background'],
        'text': '<span style="color: cyan">{}</span>'
    }