# 环境变量文件
.env
.env.local
.env.production

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# 虚拟环境
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/

# IDE
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
logs/*.log
logs/applog/*.log
logs/generations/*/
logs/reviews/*/

# 临时文件
*.tmp
*.temp
.DS_Store
Thumbs.db

# Streamlit
.streamlit/

# 测试
.pytest_cache/
.coverage
htmlcov/
.tox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 数据文件
*.db
*.sqlite
*.sqlite3

# 配置文件中的敏感信息（保留模板）
settings/config.ini.backup

# 可选：如果配置文件包含敏感信息，也可以忽略
# settings/config.ini
