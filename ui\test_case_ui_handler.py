"""测试用例 UI 处理模块"""

import streamlit as st
import datetime
from typing import Optional, Dict, Any

from models.model_utils import initialize_models
from models.base_handler import BaseHandler
from models import model_manager
from utils.logger import setup_logger
from ui.components import render_generator_result, render_reviewer_result

logger = setup_logger()

class TestCaseUIHandler:
    """测试用例 UI 处理类"""
    
    def __init__(self):
        self.base_handler = BaseHandler()
    
    @staticmethod
    async def handle_generation_ui(model_handler, conf: dict, is_single_mode: bool, roles, system_messages, test_params):
        """处理生成 UI 逻辑"""        
        try:
            if st.session_state.generation_state == "running":
                # 每次运行前重新初始化模型
                with st.spinner("正在初始化模型..."):
                    # 确保先关闭之前的会话
                    try:
                        await model_manager.close()
                    except Exception:
                        pass
                    # 重新初始化模型
                    await initialize_models(st.session_state.config_manager)
                    st.session_state.model_initialized = True
                    logger.info("模型已重新初始化")
                
                # 根据不同模式处理测试用例
                with st.spinner("正在处理测试用例..." if is_single_mode else "正在生成并评审测试用例..."):
                    handler = TestCaseUIHandler()
                    if is_single_mode:
                        await handler._handle_single_mode_generation(
                            model_handler, system_messages[0], roles.generator, test_params
                        )
                    else:
                        await handler._handle_dual_mode_generation(
                            model_handler, test_params, system_messages, roles
                        )
            
        except Exception as e:
            logger.error(f"生成处理失败: {str(e)}")
            st.error(f"生成处理失败: {str(e)}")
            st.session_state.generation_state = "ready"
        finally:
            try:
                await model_manager.close()
            except Exception as cleanup_e:
                logger.error(f"清理资源时出错: {str(cleanup_e)}")
                
    async def _handle_single_mode_generation(self, model_handler, system_message, generator_model, test_params):
        """处理单模式生成"""
        cases, error, used_model = await model_handler.handle_generator_mode(
            st.session_state.get('user_input', ''),
            system_message,
            generator_model,
            test_params
        )
        
        if error:
            st.error(error)
            st.session_state.generation_state = "ready"
            return
            
        result_meta = {
            "type": "generation",
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "result": {
                "type": "generation",
                "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "mode": "single",
                "model_info": {
                    "generator": used_model
                },
                "generator_responses": [cases[0]],
                "reviewer_responses": []
            }
        }
        
        await self.base_handler.save_results(
            generator_responses=cases,
            reviewer_responses=None,
            case_list=cases,
            result_info=result_meta
        )
        
        st.session_state.generation_state = "completed"
        st.success("✅ 测试用例生成完成！")
        render_generator_result(cases)
        
        if not st.session_state.get('generator_responses'):
            st.session_state.generator_responses = cases
    
    async def _handle_dual_mode_generation(self, model_handler, test_params, system_messages, roles):
        """处理双模式生成和评审
        
        Args:
            model_handler: 模式处理器实例
            test_params: 测试参数
            system_messages: 系统消息元组(生成器消息,评审器消息)
            roles: 角色配置对象
        """
        result = await model_handler.handle_dual_mode(
            st.session_state.get('user_input', ''),
            test_params,
            system_messages,
            (roles.generator, roles.reviewer)
        )
        
        if result.error:
            st.error(result.error)
            st.session_state.generation_state = "ready"
            st.rerun()
            return
            
        st.session_state.generator_responses = result.cases
        st.session_state.reviewer_responses = result.reviews
        
        result_meta = {
            "type": "dual",
            "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "result": "测试用例生成并评审成功",
            "model_info": {
                "generator": result.generator_model,
                "reviewer": roles.reviewer
            },
            "generator_responses": result.cases,
            "reviewer_responses": result.reviews
        }
        
        await self.base_handler.save_results(
            generator_responses=result.cases,
            reviewer_responses=result.reviews,
            case_list=result.cases,
            result_info=result_meta
        )
        
        st.session_state.generation_state = "completed"
        st.success("✅ 测试用例生成并评审完成！")
        render_generator_result(result.cases)
        render_reviewer_result(result.reviews)
    
    @staticmethod
    async def handle_review_ui(model_handler, reviewer_model: str, test_cases_str: str, review_count: int):
        """处理评审 UI 逻辑"""
        try:
            with st.spinner("正在评审测试用例..."):
                from tipwords.prompt_word_template import TESTCASE_READER_SYSTEM_MESSAGE_PATH
                
                # 每次评审前重新初始化模型
                logger.info("开始初始化评审模型...")
                
                # 确保先关闭之前的会话
                try:
                    await model_manager.close()
                except Exception as e:
                    logger.warning(f"关闭现有会话时出错: {str(e)}")
                
                # 重新初始化模型
                try:
                    await initialize_models(st.session_state.config_manager)
                    st.session_state.model_initialized = True
                    loaded_models = list(model_manager.models.keys())
                    logger.info(f"模型初始化成功，已加载模型: {loaded_models}")
                    
                    if not loaded_models:
                        raise ValueError("没有成功加载任何模型，请检查配置文件中的choice设置")
                        
                    if reviewer_model not in loaded_models:
                        raise ValueError(f"评审模型 {reviewer_model} 未在已加载的模型 {loaded_models} 中")
                except Exception as e:
                    logger.error(f"模型初始化失败: {str(e)}")
                    raise ValueError(f"模型初始化失败: {str(e)}")
                    
                # 获取系统评审提示词
                system_reader_message = st.session_state.get("system_reader_message", "")
                if not system_reader_message:
                    with open(TESTCASE_READER_SYSTEM_MESSAGE_PATH, "r", encoding="utf-8") as f:
                        system_reader_message = f.read()
                if not system_reader_message:
                    st.error("系统评审提示词未设置且无法从文件读取，请检查配置。")
                    return
                    
                # 调用评审逻辑
                review_results, error = await model_handler.handle_reviewer_mode(
                    test_cases_str,
                    system_reader_message,
                    reviewer_model,
                    review_count
                )
                
                if error:
                    st.error(f"评审失败: {error}")
                    st.session_state.review_state = "ready"
                    return
                    
                # 保存评审结果
                result_meta = {
                    "type": "review_only",
                    "timestamp": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "result": "仅评审模式测试用例评审完成",
                    "mode": "single",
                    "model_info": {
                        "reviewer": reviewer_model
                    },
                    "generator_responses": [test_cases_str],
                    "reviewer_responses": review_results
                }
                
                handler = TestCaseUIHandler()
                try:
                    await handler.base_handler.save_results(
                        generator_responses=[test_cases_str],
                        reviewer_responses=review_results,
                        case_list=[test_cases_str],
                        result_info=result_meta,
                        is_review_only=True
                    )
                    st.session_state.review_state = "completed"
                    st.success("评审成功完成并保存！")
                    render_reviewer_result(review_results)
                    
                except Exception as save_error:
                    logger.error(f"保存评审结果失败: {str(save_error)}")
                    st.warning("评审完成，但保存结果失败")
                    render_reviewer_result(review_results)
                    st.session_state.review_state = "completed"
                    
        except Exception as e:
            logger.error(f"评审处理失败: {str(e)}")
            st.error(f"评审处理失败: {str(e)}")
            st.session_state.review_state = "ready"
        finally:
            try:
                await model_manager.close()
            except Exception as cleanup_e:
                logger.error(f"清理资源时出错: {str(cleanup_e)}")
