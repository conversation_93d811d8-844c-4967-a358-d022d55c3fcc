"""
异步处理工具模块
为Streamlit应用提供安全的异步操作支持
"""

import asyncio
import functools
import threading
from typing import Any, Callable, Coroutine, TypeVar, Optional
import logging
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)

T = TypeVar('T')


class AsyncManager:
    """异步操作管理器，适配Streamlit环境"""
    
    _instance = None
    _executor = None
    _loop = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if self._executor is None:
            self._executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="async_worker")
            logger.info("异步管理器初始化完成")
    
    def run_async(self, coro: Coroutine[Any, Any, T]) -> T:
        """
        在Streamlit环境中安全地运行异步函数
        
        Args:
            coro: 协程对象
            
        Returns:
            协程的执行结果
        """
        try:
            # 检查是否已有事件循环在运行
            loop = asyncio.get_running_loop()
            # 如果有运行中的循环，使用线程池执行
            future = self._executor.submit(self._run_in_thread, coro)
            return future.result(timeout=30)  # 30秒超时
        except RuntimeError:
            # 没有运行中的循环，直接运行
            return asyncio.run(coro)
        except Exception as e:
            logger.error(f"异步执行失败: {str(e)}")
            raise
    
    def _run_in_thread(self, coro: Coroutine[Any, Any, T]) -> T:
        """在新线程中运行协程"""
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            return loop.run_until_complete(coro)
        finally:
            loop.close()
    
    def run_sync(self, func: Callable[..., T], *args, **kwargs) -> T:
        """
        运行同步函数（在线程池中）
        
        Args:
            func: 同步函数
            *args: 位置参数
            **kwargs: 关键字参数
            
        Returns:
            函数执行结果
        """
        try:
            future = self._executor.submit(func, *args, **kwargs)
            return future.result(timeout=30)
        except Exception as e:
            logger.error(f"同步执行失败: {str(e)}")
            raise
    
    def cleanup(self):
        """清理资源"""
        if self._executor:
            self._executor.shutdown(wait=True)
            self._executor = None
            logger.info("异步管理器已清理")


def async_to_sync(coro_func: Callable[..., Coroutine[Any, Any, T]]) -> Callable[..., T]:
    """
    装饰器：将异步函数转换为同步函数，适用于Streamlit
    
    Args:
        coro_func: 异步函数
        
    Returns:
        同步函数
    """
    @functools.wraps(coro_func)
    def wrapper(*args, **kwargs):
        coro = coro_func(*args, **kwargs)
        return async_manager.run_async(coro)
    
    return wrapper


def safe_async_call(coro: Coroutine[Any, Any, T], default: Optional[T] = None) -> T:
    """
    安全地调用异步函数，如果失败返回默认值
    
    Args:
        coro: 协程对象
        default: 失败时的默认返回值
        
    Returns:
        协程执行结果或默认值
    """
    try:
        return async_manager.run_async(coro)
    except Exception as e:
        logger.error(f"异步调用失败: {str(e)}")
        if default is not None:
            return default
        raise


# 创建全局实例
async_manager = AsyncManager()

__all__ = [
    'AsyncManager',
    'async_manager', 
    'async_to_sync',
    'safe_async_call'
]
