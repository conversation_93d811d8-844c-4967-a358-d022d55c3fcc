"""
UI组件模块，负责管理应用程序的各个UI组件。
"""

import streamlit as st
import streamlit.components.v1 as components
from typing import List, Tuple, Dict, Optional
from io import BytesIO
import xlsxwriter
import base64
from pathlib import Path
import os
import re
import json


from utils.config_manager import ModelConfig
from models.result_parser import ResultParser
from download.download_manager import DownloadManager
from models.role_manager import role_manager  





def render_model_config(conf: dict, model_name: str, base_url_list: List[str], 
                       model_list: List[str]) -> Tuple[bool, Optional[ModelConfig]]:
    """
    渲染模型配置组件

    Args:
        conf: 配置字典
        model_name: 模型名称
        base_url_list: 基础URL列表
        model_list: 模型列表

    Returns:
        Tuple[bool, Optional[ModelConfig]]: (是否启用模型, 模型配置对象)
    """
    try:
        model_enabled = st.checkbox(model_name, eval(conf[model_name]['choice']))
        
        cols = st.columns([2, 2, 2])
        
        if not model_enabled:
            return model_enabled, None
        
        # 获取用户输入
        api_key = cols[0].text_input(
            f"{model_name}_api_key",
            placeholder="sk-xxxxxxxxxxxxx",
            value=conf[model_name]['api_key']
        )
        
        base_url = cols[1].selectbox(
            "base_url", 
            base_url_list[:-1],
            index=base_url_list.index(conf[model_name]['base_url'])
        )
        
        model = cols[2].selectbox(
            "model", 
            model_list[:-1],
            index=model_list.index(conf[model_name]['model'])
        )
        
        max_tokens = cols[0].number_input(
            f"{model_name}最大输出Token:",
            max_value=8000,
            min_value=0,
            value=int(conf[model_name]['tokens']),
            help="1个英文字符 ≈ 0.3 个 token。1个中文字符 ≈ 0.6 个 token。最大设置8000"
        )
        
        temperature = cols[1].number_input(
            f"{model_name}模型随机性参数temperature:",
            max_value=1.3,
            min_value=0.0,
            value=float(conf[model_name]['temperature']),
            help="模型随机性参数，数字越大，生成的结果随机性越大，一般为0.7。最大设置1.3"
        )
        
        top_p = cols[2].number_input(
            f"{model_name}模型随机性参数top:",
            max_value=0.9,
            min_value=0.5,
            value=float(conf[model_name]['top']),
            help="模型随机性参数，接近1时：模型几乎会考虑所有可能的词。最大设置0.9"
        )
        
        # 创建ModelConfig实例
        model_config = ModelConfig(
            choice=model_enabled,
            api_key=api_key,
            base_url=base_url,
            name=model,
            tokens=max_tokens,
            temperature=temperature,
            top=top_p,
            base_url_list=base_url_list[:-1],
            model_list=model_list[:-1],
            model_info={}
        )
        
        # 验证配置
        model_config.validate()
        
        return model_enabled, model_config
        
    except ValueError as e:
        st.error(f"配置验证失败: {str(e)}")
        return model_enabled, None
    except Exception as e:
        st.error(f"处理配置时出错: {str(e)}")
        return model_enabled, None


def render_jquery() -> None:
    """渲染JQuery组件"""
    js_code = '''
    $(document).ready(function(){
        $("footer", window.parent.document).remove()
    });
    '''
    
    components.html(
        f'''<script src="https://cdn.bootcdn.net/ajax/libs/jquery/2.2.4/jquery.min.js"></script>
        <script>{js_code}</script>''',
        width=0,
        height=0
    )


def render_generator_result(responses, model_name=None, error_msg=None):
    # 仅用于测试用例生成页面，显示当前配置的模型
    if error_msg:
        st.error(error_msg)
        return
    # 优先用传入参数，其次用 role_manager，再用 session_state，最后用默认值
    if not model_name:
        try:
            from models.role_manager import role_manager
            model_name = getattr(role_manager, "generator_model", None)
        except Exception:
            model_name = None
        if not model_name:
            model_name = st.session_state.get('generator_model', None)
        if not model_name:
            model_name = "未知模型"
    st.markdown(f"#### {model_name}生成的测试用例")
    for case in responses:
        md = ResultParser.extract_latest_markdown(case)
        st.markdown(md, unsafe_allow_html=True)

def render_reviewer_result(responses, model_name=None, error_msg=None):
    """
    渲染评审者结果，包括评审分析和最终评审表格。
    """
    if error_msg:
        st.error(error_msg)
        return

    if not model_name:
        try:
            from models.role_manager import role_manager
            model_name = getattr(role_manager, "reviewer_model", None)
        except Exception:
            model_name = None
        if not model_name:
            model_name = st.session_state.get('reviewer_model', None)
        if not model_name:
            model_name = "未知模型"

    for idx, resp in enumerate(responses):
        st.markdown(f"##### 第{idx+1}轮评审结果")
        # 正则提取所有markdown表格
        pattern = r'(\|.+\|\n(?:\|[-: ]+\|\n)?(?:\|.*\|\n)+)'
        last_end = 0
        for m in re.finditer(pattern, resp):
            # 渲染表格前的普通内容
            if m.start() > last_end:
                st.markdown(resp[last_end:m.start()], unsafe_allow_html=True)
            # 渲染表格
            st.markdown(m.group(0), unsafe_allow_html=True)
            last_end = m.end()
        # 渲染最后一段非表格内容
        if last_end < len(resp):
            st.markdown(resp[last_end:], unsafe_allow_html=True)
        st.markdown("---")  # 添加分隔线

def render_history_generator_result(responses, model_name):
    """仅用于历史数据页面"""
    st.markdown(f"#### {model_name}生成的测试用例")
    for case in responses:
        # 提取测试用例的markdown内容
        result = ResultParser.extract_all(case)
        
        # 1. 显示markdown代码块的内容
        for block in result['markdown_blocks']:
            st.markdown(block, unsafe_allow_html=True)
            
        # 2. 如果没有markdown代码块，则显示纯文本
        if not result['markdown_blocks'] and result['plain_text']:
            st.markdown(result['plain_text'], unsafe_allow_html=True)


def render_history_data_view(data, selected, logs_dir):
    """
    历史数据展示与导出区块
    """
    col1, col2 = st.columns([2, 1])
    with col1:
        render_generator_result(data.get("deepseek_responses", []))
        render_reviewer_result(data.get("qwen_responses", []))
        if data.get("result"):
            st.info(f"结果描述：{data['result']}")
    with col2:
        st.markdown("#### 导出历史数据")
        if st.download_button(
            label="下载result.json",
            data=json.dumps(data, ensure_ascii=False, indent=2),
            file_name=f"{selected}_result.json",
            mime="application/json"
        ):
            st.success("下载成功！")

        for ext in [ "md", "xlsx"]:
            file_path = os.path.join(logs_dir, selected, f"testcases.{ext}" if ext != "docx" else "responses.docx")
            if os.path.exists(file_path):
                with open(file_path, "rb") as f:
                    if st.download_button(
                        label=f"下载{os.path.basename(file_path)}",
                        data=f.read(),
                        file_name=os.path.basename(file_path),
                        mime="application/octet-stream"
                    ):
                        st.success("下载成功！")