import os
import json
from typing import Dict, Any, Optional
# 导入自定义模块
from utils.logger import setup_logger

# 设置日志记录器
logger = setup_logger()

def save_result_to_json(data: Dict[str, Any], output_dir: str) -> str:
    """
    将AI响应内容保存为 result.json 文件
    Args:
        data: 需要保存的数据字典
        output_dir: 保存目录（如 logs/20250504_010259/）
    Returns:
        result.json 的完整路径
    """
    try:
        os.makedirs(output_dir, exist_ok=True)
        result_path = os.path.join(output_dir, "result.json")
        with open(result_path, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        logger.info(f"成功保存 result.json 到 {result_path}")
        return result_path
    except Exception as e:
        logger.error(f"保存 result.json 失败: {str(e)}", exc_info=True)
        raise

def load_latest_result_json(logs_dir: str) -> Optional[Dict[str, Any]]:
    """
    自动查找 logs/ 下最新的 result.json 并加载
    Args:
        logs_dir: logs 目录路径
    Returns:
        加载的数据字典，若无则返回 None
    """
    if not os.path.exists(logs_dir):
        logger.warning("logs 目录不存在")
        return None

    subdirs = [d for d in os.listdir(logs_dir) if os.path.isdir(os.path.join(logs_dir, d))]
    if not subdirs:
        logger.warning("logs 目录下无子目录")

    subdirs = sorted(subdirs, reverse=True)

    for sub in subdirs:
        result_path = os.path.join(logs_dir, sub, "result.json")
        if os.path.exists(result_path):
            try:
                with open(result_path, "r", encoding="utf-8") as f:
                    result_data = json.load(f)
                logger.info(f"成功加载 result.json: {result_path}")
                return result_data
            except json.JSONDecodeError as je:
                logger.error(f"JSON 解码失败: {result_path} - {str(je)}")
            except Exception as e:
                logger.error(f"加载 result.json 出错: {result_path} - {str(e)}")

    logger.warning("未找到有效的 result.json 文件")
    return None
