"""
错误处理器测试
"""

import pytest
import logging
from unittest.mock import patch, MagicMock
import sys
from pathlib import Path

# 添加项目根目录到路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from utils.error_handler import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, 
    AppError, 
    ConfigError, 
    ModelError, 
    APIError,
    ValidationError,
    ErrorLevel,
    handle_exceptions,
    safe_execute
)


class TestErrorHandler:
    """错误处理器测试类"""
    
    def test_app_error_creation(self):
        """测试自定义异常创建"""
        error = AppError("测试错误", "TEST_ERROR", ErrorLevel.WARNING)
        assert error.message == "测试错误"
        assert error.error_code == "TEST_ERROR"
        assert error.level == ErrorLevel.WARNING
    
    def test_specific_error_types(self):
        """测试特定错误类型"""
        config_error = ConfigError("配置错误")
        assert config_error.error_code == "CONFIG_ERROR"
        assert config_error.level == ErrorLevel.ERROR
        
        model_error = ModelError("模型错误")
        assert model_error.error_code == "MODEL_ERROR"
        
        api_error = APIError("API错误")
        assert api_error.error_code == "API_ERROR"
        
        validation_error = ValidationError("验证错误")
        assert validation_error.error_code == "VALIDATION_ERROR"
        assert validation_error.level == ErrorLevel.WARNING
    
    @patch('streamlit.error')
    @patch('utils.error_handler.logger')
    def test_handle_error_app_error(self, mock_logger, mock_st_error):
        """测试处理自定义应用错误"""
        error = ConfigError("配置文件格式错误")
        ErrorHandler.handle_error(error, "测试上下文")
        
        # 验证日志记录
        mock_logger.error.assert_called_once()
        # 验证Streamlit错误显示
        mock_st_error.assert_called_once()
    
    @patch('streamlit.warning')
    @patch('utils.error_handler.logger')
    def test_handle_error_warning_level(self, mock_logger, mock_st_warning):
        """测试处理警告级别错误"""
        error = ValidationError("数据格式不正确")
        ErrorHandler.handle_error(error, "验证上下文")
        
        # 验证日志记录
        mock_logger.warning.assert_called_once()
        # 验证Streamlit警告显示
        mock_st_warning.assert_called_once()
    
    @patch('streamlit.error')
    @patch('utils.error_handler.logger')
    def test_handle_error_system_error(self, mock_logger, mock_st_error):
        """测试处理系统错误"""
        error = ValueError("系统内部错误")
        ErrorHandler.handle_error(error, "系统上下文")
        
        # 验证日志记录
        mock_logger.error.assert_called_once()
        # 验证Streamlit错误显示
        mock_st_error.assert_called_once()
    
    def test_handle_exceptions_decorator_success(self):
        """测试异常处理装饰器 - 成功情况"""
        @handle_exceptions(context="测试函数", default_return="默认值")
        def test_function():
            return "成功"
        
        result = test_function()
        assert result == "成功"
    
    @patch('utils.error_handler.ErrorHandler.handle_error')
    def test_handle_exceptions_decorator_failure(self, mock_handle_error):
        """测试异常处理装饰器 - 失败情况"""
        @handle_exceptions(context="测试函数", default_return="默认值")
        def test_function():
            raise ValueError("测试错误")
        
        result = test_function()
        assert result == "默认值"
        mock_handle_error.assert_called_once()
    
    @patch('utils.error_handler.ErrorHandler.handle_error')
    def test_handle_exceptions_decorator_reraise(self, mock_handle_error):
        """测试异常处理装饰器 - 重新抛出异常"""
        @handle_exceptions(context="测试函数", reraise=True)
        def test_function():
            raise ValueError("测试错误")
        
        with pytest.raises(ValueError):
            test_function()
        mock_handle_error.assert_called_once()
    
    def test_safe_execute_success(self):
        """测试安全执行函数 - 成功情况"""
        def test_function(x, y):
            return x + y
        
        result = safe_execute(test_function, 1, 2)
        assert result == 3
    
    @patch('utils.error_handler.ErrorHandler.handle_error')
    def test_safe_execute_failure(self, mock_handle_error):
        """测试安全执行函数 - 失败情况"""
        def test_function():
            raise ValueError("测试错误")
        
        result = safe_execute(test_function, default_return="默认值")
        assert result == "默认值"
        mock_handle_error.assert_called_once()
    
    def test_get_user_friendly_message(self):
        """测试获取用户友好的错误信息"""
        # 测试自定义错误
        app_error = ConfigError("配置文件格式错误")
        message = ErrorHandler._get_user_friendly_message(app_error, "CONFIG_ERROR")
        assert message == "配置文件格式错误"
        
        # 测试系统错误
        system_error = ValueError("系统错误")
        message = ErrorHandler._get_user_friendly_message(system_error, "SYSTEM_ERROR")
        assert "系统内部错误" in message


if __name__ == "__main__":
    pytest.main([__file__])
