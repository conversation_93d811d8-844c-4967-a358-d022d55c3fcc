#!/usr/bin/env python3
"""
测试登录页面是否正确显示输入框
"""

import requests
import time
from bs4 import BeautifulSoup

def test_login_page():
    """测试登录页面"""
    print("=== 测试登录页面显示 ===")
    
    try:
        # 请求登录页面
        print("1. 请求登录页面...")
        response = requests.get("http://localhost:8519", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ 页面请求失败: {response.status_code}")
            return
        
        # 解析HTML
        soup = BeautifulSoup(response.text, 'html.parser')
        
        print("2. 检查登录表单元素...")
        
        # 查找输入框
        text_inputs = soup.find_all('input', {'type': 'text'})
        password_inputs = soup.find_all('input', {'type': 'password'})
        buttons = soup.find_all('button')
        
        print(f"   找到文本输入框: {len(text_inputs)} 个")
        print(f"   找到密码输入框: {len(password_inputs)} 个")
        print(f"   找到按钮: {len(buttons)} 个")
        
        # 检查是否有用户名输入框
        username_found = False
        for input_elem in text_inputs:
            placeholder = input_elem.get('placeholder', '')
            if '用户名' in placeholder or 'username' in placeholder.lower():
                username_found = True
                print(f"   ✅ 找到用户名输入框: {placeholder}")
                break
        
        if not username_found:
            print("   ❌ 未找到用户名输入框")
        
        # 检查是否有密码输入框
        password_found = False
        for input_elem in password_inputs:
            placeholder = input_elem.get('placeholder', '')
            if '密码' in placeholder or 'password' in placeholder.lower():
                password_found = True
                print(f"   ✅ 找到密码输入框: {placeholder}")
                break
        
        if not password_found:
            print("   ❌ 未找到密码输入框")
        
        # 检查是否有登录按钮
        login_button_found = False
        for button in buttons:
            button_text = button.get_text(strip=True)
            if '登录' in button_text or 'login' in button_text.lower():
                login_button_found = True
                print(f"   ✅ 找到登录按钮: {button_text}")
                break
        
        if not login_button_found:
            print("   ❌ 未找到登录按钮")
        
        # 总结
        if username_found and password_found and login_button_found:
            print("\n🎉 登录表单显示正常！")
        else:
            print("\n❌ 登录表单显示不完整")
            
            # 输出部分HTML用于调试
            print("\n调试信息 - 页面内容片段:")
            forms = soup.find_all('form')
            if forms:
                for i, form in enumerate(forms):
                    print(f"表单 {i+1}:")
                    print(form.prettify()[:500])
            else:
                print("未找到任何表单")
                
                # 查找包含"登录"的元素
                login_elements = soup.find_all(text=lambda text: text and '登录' in text)
                if login_elements:
                    print("包含'登录'的文本:")
                    for elem in login_elements[:5]:
                        print(f"  - {elem.strip()}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_page()
