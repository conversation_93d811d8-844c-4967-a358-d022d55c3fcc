"""
简单的登录页面实现，作为备用方案
"""

import streamlit as st
import bcrypt
import yaml
from pathlib import Path
from utils.logger import setup_logger

logger = setup_logger()

def verify_password(password: str, hashed_password: str) -> bool:
    """验证密码"""
    try:
        return bcrypt.checkpw(password.encode('utf-8'), hashed_password.encode('utf-8'))
    except Exception as e:
        logger.error(f"密码验证失败: {e}")
        return False

def load_users():
    """加载用户配置"""
    try:
        config_path = Path("settings/users.yaml")
        with open(config_path, 'r', encoding='utf-8') as file:
            config = yaml.safe_load(file)
        return config['credentials']['usernames']
    except Exception as e:
        logger.error(f"加载用户配置失败: {e}")
        return {}

def render_simple_login():
    """渲染简单登录页面"""
    st.markdown("""
    <style>
    .simple-login-container {
        max-width: 400px;
        margin: 0 auto;
        padding: 2rem;
        background: white;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
    .simple-login-title {
        text-align: center;
        color: #1f77b4;
        margin-bottom: 1rem;
    }
    </style>
    """, unsafe_allow_html=True)
    
    # 居中显示登录表单
    col1, col2, col3 = st.columns([1, 2, 1])
    
    with col2:
        st.markdown('<div class="simple-login-container">', unsafe_allow_html=True)
        
        # 标题和图标
        st.markdown('<h2 class="simple-login-title">🔐 AI测试用例生成工具</h2>', unsafe_allow_html=True)
        st.markdown('<p style="text-align: center; color: #666;">请登录以继续使用</p>', unsafe_allow_html=True)
        
        # 登录表单
        with st.form("simple_login_form", clear_on_submit=False):
            username = st.text_input("用户名", placeholder="请输入用户名")
            password = st.text_input("密码", type="password", placeholder="请输入密码")
            submit_button = st.form_submit_button("登录", use_container_width=True)
            
            if submit_button:
                if username and password:
                    # 验证用户
                    users = load_users()
                    if username in users:
                        user_data = users[username]
                        if verify_password(password, user_data['password']):
                            # 登录成功
                            st.session_state.authentication_status = True
                            st.session_state.username = username
                            st.session_state.name = user_data['name']
                            st.session_state.user_info = {
                                'username': username,
                                'name': user_data['name'],
                                'email': user_data['email'],
                                'role': user_data.get('role', 'user')
                            }
                            logger.info(f"用户 {username} 登录成功（简单登录）")
                            st.success("登录成功！")
                            st.rerun()
                        else:
                            st.error("用户名或密码错误")
                    else:
                        st.error("用户名或密码错误")
                else:
                    st.warning("请输入用户名和密码")
        
        # 显示默认账号信息（仅在开发环境）
        with st.expander("💡 默认账号信息"):
            st.info("""
            **默认管理员账号：**
            - 用户名: admin
            - 密码: admin123
            
            首次登录后请及时修改密码！
            """)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    return None, None, None

def render_fallback_login():
    """渲染备用登录页面（当主登录系统失败时使用）"""
    st.warning("⚠️ 主登录系统暂时不可用，正在使用备用登录系统")
    return render_simple_login()
