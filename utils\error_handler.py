"""
统一错误处理模块
提供一致的错误处理和用户友好的错误信息
"""

import logging
import traceback
import functools
from typing import Any, Callable, Optional, Type, Union
from enum import Enum
import streamlit as st

logger = logging.getLogger(__name__)


class ErrorLevel(Enum):
    """错误级别枚举"""
    INFO = "info"
    WARNING = "warning" 
    ERROR = "error"
    CRITICAL = "critical"


class AppError(Exception):
    """应用程序自定义异常基类"""
    
    def __init__(self, message: str, error_code: str = None, level: ErrorLevel = ErrorLevel.ERROR):
        self.message = message
        self.error_code = error_code or "UNKNOWN_ERROR"
        self.level = level
        super().__init__(self.message)


class ConfigError(AppError):
    """配置相关错误"""
    
    def __init__(self, message: str, error_code: str = "CONFIG_ERROR"):
        super().__init__(message, error_code, ErrorLevel.ERROR)


class ModelError(AppError):
    """模型相关错误"""
    
    def __init__(self, message: str, error_code: str = "MODEL_ERROR"):
        super().__init__(message, error_code, ErrorLevel.ERROR)


class APIError(AppError):
    """API调用相关错误"""
    
    def __init__(self, message: str, error_code: str = "API_ERROR"):
        super().__init__(message, error_code, ErrorLevel.ERROR)


class ValidationError(AppError):
    """数据验证错误"""
    
    def __init__(self, message: str, error_code: str = "VALIDATION_ERROR"):
        super().__init__(message, error_code, ErrorLevel.WARNING)


class ErrorHandler:
    """统一错误处理器"""
    
    @staticmethod
    def handle_error(error: Exception, context: str = "", show_user: bool = True) -> None:
        """
        统一处理错误
        
        Args:
            error: 异常对象
            context: 错误上下文信息
            show_user: 是否向用户显示错误
        """
        error_msg = str(error)
        full_context = f"{context}: {error_msg}" if context else error_msg
        
        # 记录详细错误信息
        if isinstance(error, AppError):
            level = error.level
            error_code = error.error_code
        else:
            level = ErrorLevel.ERROR
            error_code = "SYSTEM_ERROR"
        
        # 记录日志
        if level == ErrorLevel.CRITICAL:
            logger.critical(f"[{error_code}] {full_context}", exc_info=True)
        elif level == ErrorLevel.ERROR:
            logger.error(f"[{error_code}] {full_context}", exc_info=True)
        elif level == ErrorLevel.WARNING:
            logger.warning(f"[{error_code}] {full_context}")
        else:
            logger.info(f"[{error_code}] {full_context}")
        
        # 向用户显示友好的错误信息
        if show_user:
            ErrorHandler._show_user_error(error, error_code, level)
    
    @staticmethod
    def _show_user_error(error: Exception, error_code: str, level: ErrorLevel) -> None:
        """向用户显示错误信息"""
        user_message = ErrorHandler._get_user_friendly_message(error, error_code)
        
        if level == ErrorLevel.CRITICAL:
            st.error(f"🚨 严重错误: {user_message}")
        elif level == ErrorLevel.ERROR:
            st.error(f"❌ 错误: {user_message}")
        elif level == ErrorLevel.WARNING:
            st.warning(f"⚠️ 警告: {user_message}")
        else:
            st.info(f"ℹ️ 信息: {user_message}")
    
    @staticmethod
    def _get_user_friendly_message(error: Exception, error_code: str) -> str:
        """获取用户友好的错误信息"""
        if isinstance(error, AppError):
            return error.message
        
        # 根据错误类型提供友好的消息
        error_messages = {
            "CONFIG_ERROR": "配置文件有误，请检查设置",
            "MODEL_ERROR": "模型配置或调用出现问题",
            "API_ERROR": "API调用失败，请检查网络连接和API密钥",
            "VALIDATION_ERROR": "输入数据格式不正确",
            "SYSTEM_ERROR": "系统内部错误，请稍后重试"
        }
        
        return error_messages.get(error_code, str(error))


def handle_exceptions(
    context: str = "",
    show_user: bool = True,
    default_return: Any = None,
    reraise: bool = False
):
    """
    异常处理装饰器
    
    Args:
        context: 错误上下文
        show_user: 是否向用户显示错误
        default_return: 异常时的默认返回值
        reraise: 是否重新抛出异常
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                ErrorHandler.handle_error(e, context or func.__name__, show_user)
                if reraise:
                    raise
                return default_return
        return wrapper
    return decorator


def safe_execute(
    func: Callable,
    *args,
    context: str = "",
    default_return: Any = None,
    show_user: bool = True,
    **kwargs
) -> Any:
    """
    安全执行函数，捕获并处理异常
    
    Args:
        func: 要执行的函数
        *args: 位置参数
        context: 错误上下文
        default_return: 异常时的默认返回值
        show_user: 是否向用户显示错误
        **kwargs: 关键字参数
        
    Returns:
        函数执行结果或默认值
    """
    try:
        return func(*args, **kwargs)
    except Exception as e:
        ErrorHandler.handle_error(e, context or func.__name__, show_user)
        return default_return


# 导出
__all__ = [
    'ErrorLevel',
    'AppError',
    'ConfigError', 
    'ModelError',
    'APIError',
    'ValidationError',
    'ErrorHandler',
    'handle_exceptions',
    'safe_execute'
]
