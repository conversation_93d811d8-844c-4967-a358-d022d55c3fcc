"""生成器处理模块"""

import streamlit as st
from typing import Optional, List, Tuple
import datetime

from models.base_handler import BaseHandler
from models.generate_utils import generate_test_cases
from utils.logger import setup_logger

logger = setup_logger()

class GeneratorHandler(BaseHandler):
    """生成器处理类"""
    
    async def handle_generation(
        self,
        user_input: str,
        system_message: str,
        model_name: str,
        test_params: dict
    ) -> Tuple[List[str], Optional[str], str]:
        """处理测试用例生成"""
        try:
            # 记录开始时间
            start_time = datetime.datetime.now()
            logger.info(f"开始使用模型 {model_name}生成测试用例")
            
            # 调用模型生成测试用例
            cases = await generate_test_cases(
                user_input,
                system_message,
                test_params['priority'],
                test_params['count'],
                test_params['rates'],
                model_name=model_name
            )
            
            # 计算耗时
            time_taken = (datetime.datetime.now() - start_time).total_seconds()
            logger.info(f"测试用例生成完成，耗时: {time_taken:.1f}秒")
            
            return cases, None, model_name
            
        except Exception as e:
            error_msg = f"生成测试用例失败: {str(e)}"
            logger.error(error_msg)
            return [], error_msg, model_name
