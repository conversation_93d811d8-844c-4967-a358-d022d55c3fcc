"""
模型配置模块，整合模型配置定义和管理功能。
包含：
1. 模型配置的数据结构定义
2. 默认配置信息
3. 配置管理和UI交互
"""

from typing import Dict, List, Optional, TypedDict, Union, Tuple, Any
from dataclasses import dataclass
import streamlit as st
import logging
from utils.constants import model_constants

#  默认配置
from llms import DEEPSEEK_MODEL_INFO, QWEN_MODEL_INFO, MODEL_CONFIGS

logger = logging.getLogger(__name__)

# 数据结构定义
class ModelParameters(TypedDict):
    """模型参数类型定义"""
    max_tokens: int
    temperature: float
    top_p: float


class ModelInfo(TypedDict):
    """模型信息类型定义"""
    name: str
    parameters: ModelParameters
    family: str
    functions: List[Dict]
    vision: bool
    json_output: bool
    function_calling: bool
    structured_output: bool


@dataclass
class ModelConfig:
    """模型配置数据类"""
    name: str
    base_url: str
    api_key: str
    tokens: int
    temperature: float
    top: float
    choice: bool
    base_url_list: List[str]
    model_list: List[str]
    model_info: ModelInfo

    def validate(self, strict: bool = False) -> None:
        """
        验证模型配置的有效性

        Args:
            strict: 是否严格验证（包括API密钥）
        """
        if not self.name:
            raise ValueError("模型名称不能为空")
        if not self.base_url:
            raise ValueError("base_url不能为空")

        # 只在严格模式下验证API密钥
        if strict and not self.api_key:
            raise ValueError("api_key不能为空，请在.env文件中配置或设置环境变量")

        if not isinstance(self.tokens, int) or self.tokens <= 0:
            raise ValueError("tokens必须为正整数")
        if not isinstance(self.temperature, (int, float)):
            raise ValueError("temperature必须为数值类型")
        if not isinstance(self.top, (int, float)):
            raise ValueError("top必须为数值类型")
        if not isinstance(self.choice, bool):
            raise ValueError("choice必须为布尔类型")
        if not isinstance(self.base_url_list, list):
            raise ValueError("base_url_list必须为列表")
        if not isinstance(self.model_list, list):
            raise ValueError("model_list必须为列表")
        if not isinstance(self.model_info, dict):
            raise ValueError("model_info必须为字典类型")

    def has_valid_api_key(self) -> bool:
        """检查是否有有效的API密钥"""
        return bool(self.api_key and self.api_key.strip())

    def to_dict(self) -> Dict[str, Union[str, int, float, bool, List[str], ModelInfo]]:
        """转换为字典格式"""
        return {
            "name": self.name,
            "base_url": self.base_url,
            "api_key": self.api_key,
            "tokens": self.tokens,
            "temperature": self.temperature,
            "top": self.top,
            "choice": self.choice,
            "base_url_list": self.base_url_list,
            "model_list": self.model_list,
            "model_info": self.model_info
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ModelConfig':
        """从字典创建配置实例"""
        return cls(
            name=str(data["name"]),
            base_url=str(data["base_url"]),
            api_key=str(data["api_key"]),
            tokens=int(data["tokens"]),
            temperature=float(data["temperature"]),
            top=float(data["top"]),
            choice=bool(data["choice"]),
            base_url_list=list(data["base_url_list"]),
            model_list=list(data["model_list"]),
            model_info=data["model_info"]
        )


DEFAULT_MODELS = {
    model_name: ModelConfig(
        name=config["name"],
        base_url=config["base_url"],
        api_key="",  # 需要从配置文件加载
        tokens=config["tokens"],
        temperature=config["temperature"],
        top=config["top"],
        choice=config["choice"],
        base_url_list=config["base_url_list"],
        model_list=config["model_list"],
        model_info=config["model_info"]
    )
    for model_name, config in MODEL_CONFIGS.items()
}

# 配置管理器
class ModelConfigManager:
    """模型配置管理器"""
    _instance = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = False
            self.config_manager = None
    
    def initialize(self, config_manager):
        """初始化配置管理器"""
        self.config_manager = config_manager
        self._initialized = True
        
    @property
    def is_initialized(self) -> bool:
        """检查是否已初始化"""
        return self._initialized and self.config_manager is not None
    
    async def handle_model_configs(self) -> List[str]:
        """处理模型配置并返回启用的模型列表"""
        if not self.is_initialized:
            st.error("模型配置管理器未初始化")
            return []
            
        try:
            conf = self.config_manager.config
            model_enabled_dict = {}
            
            valid_models = [name for name in conf.sections() 
                          if name in DEFAULT_MODELS]
            if not valid_models:
                st.warning("未找到有效的模型配置，请检查配置文件")
                return []

            # 处理每个模型配置
            for model_name in valid_models:
                try:
                    base_url_list = conf[model_name]['base_url_list'].split(',')
                    model_list = conf[model_name]['model_list'].split(',')
                    enabled, config = self._process_model_ui(conf, model_name, base_url_list, model_list)
                    if enabled and config:
                        model_enabled_dict[model_name] = enabled
                        await self._update_model_config(model_name, config)
                except Exception as e:
                    st.error(f"处理模型 {model_name} 配置失败: {str(e)}")
                    continue

            # 处理配置保存
            if st.button("保存模型设置", key="save_model_settings"):
                await self._save_configs(conf, model_enabled_dict)
                st.session_state.model_initialized = True
                st.session_state.generation_state = "ready"
                st.success("模型配置已保存")
                
            return self._get_enabled_models(conf)
            
        except Exception as e:
            st.error(f"处理模型配置失败: {str(e)}")
            return []   
    async def _save_configs(self, conf: dict, enabled_dict: Dict[str, bool]) -> None:
        """保存所有模型配置"""
        try:
            # 检查是否至少选择了一个模型
            if not any(enabled_dict.values()):
                st.error("❌ 请至少选择一个模型！")
                return
            
            # 更新配置
            for model_name in model_constants.AVAILABLE_MODELS:
                if model_name in conf.sections():
                    conf[model_name]["choice"] = str(enabled_dict.get(model_name, False))
            logger.debug("模型配置已保存")
            # 保存配置
            self.config_manager.save_config()
        except Exception as e:
            logger.error(f"保存配置失败: {str(e)}", exc_info=True)
            raise

    def _get_enabled_models(self, conf: dict) -> List[str]:
        """获取已启用的模型列表"""
        return [name for name in conf.sections() 
                if conf[name].get('choice', 'False').lower() in ['true', '1']]

    def _process_model_ui(self, conf: dict, model_name: str, base_url_list: List[str], 
                         model_list: List[str]) -> Tuple[bool, Optional[ModelConfig]]:
        """处理单个模型的UI配置
        
        Args:
            conf: 配置字典
            model_name: 模型名称
            base_url_list: 基础URL列表
            model_list: 模型列表
            
        Returns:
            Tuple[bool, Optional[ModelConfig]]: (是否启用, 模型配置)
        """
        try:
            # 渲染UI组件
            from ui.components import render_model_config
            return render_model_config(conf, model_name, base_url_list, model_list)
            
        except Exception as e:
            logger.error(f"处理模型 {model_name} UI配置失败: {str(e)}")
            return False, None

    async def _update_model_config(self, model_name: str, config: ModelConfig) -> None:
        """更新模型配置"""
        try:
            if config and self.config_manager:
                self.config_manager.update_model_config(model_name, config)
                logger.info(f"成功更新模型 {model_name} 配置")
        except Exception as e:
            logger.error(f"更新模型 {model_name} 配置失败: {str(e)}")
            raise

# 创建全局单例实例
model_config = ModelConfigManager()

# 导出
__all__ = [
    'ModelConfig', 
    'ModelInfo', 
    'DEFAULT_MODELS',
    'model_config'
]