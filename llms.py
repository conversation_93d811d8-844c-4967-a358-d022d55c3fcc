# -*- coding: utf-8 -*-

"""
模型配置中心，整合所有模型相关的配置信息。
包含：
1. 模型参数配置
2. 模型基础信息
3. 默认配置

这样改动后的主要优势：
1.配置集中管理：
所有模型相关的配置都集中在 llms.py 中
配置更改只需要修改一个文件，减少了重复
提高了维护性和一致性

2.类型定义更清晰：
使用 TypedDict 定义了明确的类型
提供了更好的代码提示和类型检查
增加了代码的可靠性

3.结构更优化：
配置层次更清晰
注释更完整
导出接口更规范

4.更易于扩展：
添加新模型只需要在 llms.py 中的 MODEL_CONFIGS 添加配置
model_config.py 自动从 MODEL_CONFIGS 构建 DEFAULT_MODELS
配置复用更方便：

其他模块可以直接导入 llms 中的配置
避免了配置在多处定义导致的不一致


"""

from typing import Dict, List, TypedDict

class ModelParameters(TypedDict):
    """模型参数类型定义"""
    max_tokens: int
    temperature: float
    top_p: float

class ModelInfo(TypedDict):
    """模型信息类型定义"""
    name: str
    parameters: ModelParameters
    family: str
    functions: List[Dict]
    vision: bool
    json_output: bool
    function_calling: bool
    structured_output: bool

# Deepseek模型配置
DEEPSEEK_MODEL_INFO: ModelInfo = {
    "name": "deepseek-chat",  # 模型名称
    "parameters": {
        "max_tokens": 2048,  # 每次输出最大token数
        # deepseek官方数据：1个英文字符 ≈ 0.3 个 token。1 个中文字符 ≈ 0.6 个 token。
        "temperature": 0.4,  # 模型随机性参数，数字越大，生成的结果随机性越大
        "top_p": 0.9,  # 模型随机性参数
    },
    "family": "gpt-4o",
    "functions": [],
    "vision": False,
    "json_output": True,
    "function_calling": True,
    "structured_output": True
}

# Qwen模型配置
QWEN_MODEL_INFO: ModelInfo = {
    "name": "qwen-chat",  # 模型名称
    "parameters": {
        "max_tokens": 4096,  # 每次输出最大token数
        "temperature": 0.7,  # 模型随机性参数
        "top_p": 0.8,  # 模型随机性参数
    },
    "family": "gpt-4o",
    "functions": [],
    "vision": False,
    "json_output": True,
    "function_calling": True,
    "structured_output": True
}

# 需要从配置文件加载的字段
RUNTIME_CONFIG_FIELDS = ['api_key', 'base_url', 'choice']

# 模型默认配置
MODEL_CONFIGS = {
    "deepseek": {
        # 静态配置（代码中固定）
        "name": "deepseek-chat",
        "tokens": 2048,
        "temperature": 0.4,
        "top": 0.9,
        "base_url_list": ["https://api.deepseek.com"],
        "model_list": ["deepseek-chat"],
        "model_info": DEEPSEEK_MODEL_INFO,
        
        # 运行时配置（从config.ini加载）
        "base_url": "https://api.deepseek.com",  # 默认值，可被配置文件覆盖
        "api_key": "",  # 必须从配置文件加载
        "choice": True  # 默认值，可被配置文件覆盖
    },
    "qwen": {
        # 静态配置（代码中固定）
        "name": "qwen-max",
        "tokens": 4096,
        "temperature": 0.7,
        "top": 0.8,
        "base_url_list": ["https://dashscope.aliyuncs.com/compatible-mode/v1"],
        "model_list": ["qwen-max"],
        "model_info": QWEN_MODEL_INFO,
        
        # 运行时配置（从config.ini加载）
        "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",  # 默认值，可被配置文件覆盖
        "api_key": "",  # 必须从配置文件加载
        "choice": True  # 默认值，可被配置文件覆盖
    }
}

# 可用模型列表
AVAILABLE_MODELS = list(MODEL_CONFIGS.keys())

# 导出
__all__ = [
    'ModelParameters',
    'ModelInfo',
    'DEEPSEEK_MODEL_INFO',
    'QWEN_MODEL_INFO',
    'MODEL_CONFIGS',
    'AVAILABLE_MODELS',
    'RUNTIME_CONFIG_FIELDS'  # 添加运行时配置字段列表
]