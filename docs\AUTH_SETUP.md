# 用户认证系统设置指南

## 🔐 认证系统概述

本系统集成了基于 `streamlit-authenticator` 的用户认证功能，支持：

- ✅ 用户登录/登出
- ✅ 基于角色的权限控制（管理员/普通用户）
- ✅ 用户管理（增删改查）
- ✅ 密码加密存储
- ✅ 会话管理和Cookie记住登录

## 🚀 快速开始

### 1. 默认管理员账号

系统首次启动时会自动创建默认管理员账号：

```
用户名: admin
密码: admin123
```

**⚠️ 重要：首次登录后请立即修改默认密码！**

### 2. 登录流程

1. 访问应用URL
2. 输入用户名和密码
3. 点击"登录"按钮
4. 登录成功后自动跳转到主界面

### 3. 用户管理（仅管理员）

管理员可以通过侧边栏的"用户管理"菜单进行：

- **查看用户列表**：显示所有用户信息和统计
- **添加新用户**：创建新的用户账号
- **修改密码**：重置任意用户的密码
- **删除用户**：删除不需要的用户账号

## 📋 权限说明

### 管理员权限 (admin)
- ✅ 访问所有功能模块
- ✅ 用户管理（增删改查）
- ✅ 修改任意用户密码
- ✅ 查看系统统计信息

### 普通用户权限 (user)
- ✅ 访问基本功能模块
- ✅ 修改自己的密码
- ❌ 无法访问用户管理
- ❌ 无法管理其他用户

## 🔧 配置文件

### 用户数据存储

用户信息存储在 `settings/users.yaml` 文件中：

```yaml
credentials:
  usernames:
    admin:
      email: <EMAIL>
      name: 系统管理员
      password: $2b$12$... # bcrypt哈希
      role: admin
    user1:
      email: <EMAIL>
      name: 测试工程师
      password: $2b$12$... # bcrypt哈希
      role: user
cookie:
  name: testcase_auth_cookie
  key: testcase_secret_key_2025
  expiry_days: 30
preauthorized:
  emails: []
```

### 安全配置

- **密码加密**：使用 bcrypt 算法进行密码哈希
- **Cookie安全**：使用加密Cookie存储会话信息
- **会话超时**：默认30天自动过期

## 🐳 Docker部署注意事项

### 1. 持久化用户数据

确保 `settings` 目录被正确挂载：

```yaml
volumes:
  - ./settings:/app/settings
```

### 2. 环境变量

可以通过环境变量自定义配置：

```bash
# 设置Cookie密钥
export AUTH_COOKIE_KEY="your_secret_key_here"

# 设置会话过期时间（天）
export AUTH_EXPIRY_DAYS=30
```

## 🔒 安全最佳实践

### 1. 密码策略
- 最小长度：6位
- 建议使用复杂密码
- 定期更换密码

### 2. 账号管理
- 及时删除不需要的账号
- 定期审查用户权限
- 监控登录活动

### 3. 部署安全
- 使用HTTPS协议
- 定期备份用户数据
- 保护配置文件安全

## 🛠️ 故障排除

### 1. 忘记管理员密码

删除 `settings/users.yaml` 文件，重启应用会重新创建默认管理员账号。

### 2. 用户无法登录

检查：
- 用户名和密码是否正确
- 用户账号是否存在
- `settings/users.yaml` 文件是否损坏

### 3. 权限问题

确认：
- 用户角色设置正确
- 功能模块权限检查正常
- 会话状态有效

## 📝 API参考

### AuthManager 主要方法

```python
# 登录检查
auth_manager.is_authenticated() -> bool

# 权限检查
auth_manager.is_admin() -> bool

# 获取当前用户
auth_manager.get_current_user() -> Dict

# 用户管理
auth_manager.add_user(username, name, email, password, role) -> bool
auth_manager.update_user_password(username, new_password) -> bool
auth_manager.delete_user(username) -> bool
```

## 🔄 升级和维护

### 1. 备份用户数据

```bash
# 备份用户配置
cp settings/users.yaml settings/users.yaml.backup
```

### 2. 更新认证库

```bash
pip install --upgrade streamlit-authenticator
```

### 3. 迁移用户数据

如需迁移到新的认证系统，可以导出现有用户数据并重新导入。

---

## 📞 技术支持

如有问题，请查看：
1. 应用日志文件
2. 本文档的故障排除部分
3. 联系系统管理员
