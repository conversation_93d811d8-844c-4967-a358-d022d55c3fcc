#!/usr/bin/env python3
"""
简单测试登录页面
"""

import requests
import re

def test_login_page():
    """测试登录页面"""
    print("=== 测试登录页面显示 ===")
    
    try:
        # 请求登录页面
        print("1. 请求登录页面...")
        response = requests.get("http://localhost:8519", timeout=10)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code != 200:
            print(f"   ❌ 页面请求失败: {response.status_code}")
            return
        
        html_content = response.text
        
        print("2. 检查登录表单元素...")
        
        # 使用正则表达式查找输入框
        text_inputs = re.findall(r'<input[^>]*type=["\']text["\'][^>]*>', html_content, re.IGNORECASE)
        password_inputs = re.findall(r'<input[^>]*type=["\']password["\'][^>]*>', html_content, re.IGNORECASE)
        buttons = re.findall(r'<button[^>]*>.*?</button>', html_content, re.IGNORECASE | re.DOTALL)
        
        print(f"   找到文本输入框: {len(text_inputs)} 个")
        print(f"   找到密码输入框: {len(password_inputs)} 个")
        print(f"   找到按钮: {len(buttons)} 个")
        
        # 检查具体内容
        username_found = False
        password_found = False
        login_button_found = False
        
        # 检查用户名输入框
        for input_elem in text_inputs:
            if '用户名' in input_elem or 'username' in input_elem.lower():
                username_found = True
                print(f"   ✅ 找到用户名输入框")
                break
        
        # 检查密码输入框
        for input_elem in password_inputs:
            if '密码' in input_elem or 'password' in input_elem.lower():
                password_found = True
                print(f"   ✅ 找到密码输入框")
                break
        
        # 检查登录按钮
        for button in buttons:
            if '登录' in button or 'login' in button.lower():
                login_button_found = True
                print(f"   ✅ 找到登录按钮")
                break
        
        # 检查是否包含登录相关文本
        if '登录' in html_content:
            print("   ✅ 页面包含'登录'文本")
        else:
            print("   ❌ 页面不包含'登录'文本")
        
        # 检查是否包含表单
        if '<form' in html_content:
            print("   ✅ 页面包含表单元素")
        else:
            print("   ❌ 页面不包含表单元素")
        
        # 总结
        if username_found and password_found and login_button_found:
            print("\n🎉 登录表单显示正常！")
        else:
            print("\n❌ 登录表单显示不完整")
            
            # 输出调试信息
            print("\n调试信息:")
            if text_inputs:
                print("文本输入框:")
                for i, inp in enumerate(text_inputs[:3]):
                    print(f"  {i+1}: {inp[:100]}...")
            
            if password_inputs:
                print("密码输入框:")
                for i, inp in enumerate(password_inputs[:3]):
                    print(f"  {i+1}: {inp[:100]}...")
            
            if buttons:
                print("按钮:")
                for i, btn in enumerate(buttons[:3]):
                    print(f"  {i+1}: {btn[:100]}...")
        
        # 检查是否有错误信息
        if 'error' in html_content.lower() or '错误' in html_content:
            print("\n⚠️  页面可能包含错误信息")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_login_page()
