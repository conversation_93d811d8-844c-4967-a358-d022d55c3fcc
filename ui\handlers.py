"""UI处理器模块"""

import streamlit as st
import os
from typing import Dict, Any
from models.role_manager import role_manager
from models.model_config import model_config
from utils.message_utils import load_system_messages
from tipwords.prompt_word_template import (
    TESTCASE_WRITER_SYSTEM_MESSAGE_PATH, 
    TESTCASE_READER_SYSTEM_MESSAGE_PATH
)
from utils.logger import setup_logger

logger = setup_logger()


async def handle_model_settings(conf: dict) -> None:
    """处理模型设置"""
    try:
        st.markdown("### 🤖 模型设置")

        # 在模型设置页面显示完整的API密钥配置状态
        from utils.startup_check import startup_checker
        startup_checker.run_startup_check(show_success_message=True)

        enabled_models = await model_config.handle_model_configs()
        if enabled_models:
            await role_manager.render_role_settings(enabled_models)
    except Exception as e:
        st.error(f"处理模型设置时发生错误: {str(e)}")

async def handle_prompt_settings() -> None:
    """处理提示词设置"""
    st.markdown("### ✍ 提示词设置")
    try:
        # 读取提示词模板
        writer_msg = ""
        reader_msg = ""
        try:
            with open(TESTCASE_WRITER_SYSTEM_MESSAGE_PATH, "r", encoding="utf-8") as f:
                writer_msg = f.read()
            with open(TESTCASE_READER_SYSTEM_MESSAGE_PATH, "r", encoding="utf-8") as f:
                reader_msg = f.read()
        except FileNotFoundError:
            st.warning("未找到提示词模板文件，将使用空模板")
        except Exception as e:
            st.error(f"读取提示词模板失败: {str(e)}")
            return

        # 编辑提示词
        new_writer_msg = st.text_area("编写测试用例提示词模板", value=writer_msg, height=600)
        new_reader_msg = st.text_area("评审测试用例提示词模板", value=reader_msg, height=600)

        # 保存提示词
        if st.button("保存提示词设置"):
            if not new_writer_msg.strip() or not new_reader_msg.strip():
                st.error("提示词内容不能为空")
                return
                
            try:
                os.makedirs(os.path.dirname(TESTCASE_WRITER_SYSTEM_MESSAGE_PATH), exist_ok=True)
                os.makedirs(os.path.dirname(TESTCASE_READER_SYSTEM_MESSAGE_PATH), exist_ok=True)
                
                with open(TESTCASE_WRITER_SYSTEM_MESSAGE_PATH, "w", encoding="utf-8") as f:
                    f.write(new_writer_msg)
                with open(TESTCASE_READER_SYSTEM_MESSAGE_PATH, "w", encoding="utf-8") as f:
                    f.write(new_reader_msg)
                    
                st.success("提示词设置已保存！")
                logger.info("提示词设置已保存成功")
                # 更新session state中的系统消息
                st.session_state["system_writer_message"] = new_writer_msg
                st.session_state["system_reader_message"] = new_reader_msg
                
            except Exception as e:
                st.error(f"保存提示词设置失败: {str(e)}")
                
    except Exception as e:
        st.error(f"处理提示词设置时发生错误: {str(e)}")

def display_current_mode(is_single_mode: bool, role_type: str) -> None:
    """显示当前模式状态"""
    st.markdown("---")
    col1, col2 = st.columns([1, 3])
    with col1:
        st.markdown("### 当前模式:")
    with col2:
        if is_single_mode:
            mode_text = "🔄 仅生成模式" if role_type == "generator" else "👀 仅评审模式"
            mode_color = "green" if role_type == "generator" else "blue"
        else:
            mode_text = "🔄👀 生成并评审模式"
            mode_color = "orange"
        st.markdown(f"### :{mode_color}[{mode_text}]")
    st.markdown("---")
