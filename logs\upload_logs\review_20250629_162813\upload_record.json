{"upload_id": "review_20250629_162813", "timestamp": "2025-06-29 16:28:13", "upload_type": "review", "file_info": {"name": "testcases.xlsx", "size": 8532, "type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "saved_path": "logs/upload_logs\\review_20250629_162813\\testcases.xlsx"}, "content": "| 用例ID | 测试目标 | 前置条件 | 操作步骤 | 预期结果 | 优先级 | 测试类型 | 关联需求 |\n| --- | --- | --- | --- | --- | --- | --- | --- |\n| LOGIN_001 | 验证标准用户名密码登录 | 1. 系统运行正常<br>2. 存在测试用户(testuser123) | 1. 输入用户名\"testuser123\"<br>2. 输入密码\"Passw0rd123\"<br>3. 点击登录按钮 | 1. 成功跳转至系统首页<br>2. 显示用户欢迎信息 | P0 | 功能验证 | 需求1 |\n| LOGIN_002 | 验证最小长度用户名 | 1. 系统运行正常<br>2. 存在用户名为6字符的用户(abcdef) | 1. 输入用户名\"abcdef\"<br>2. 输入有效密码\"Abcd1234\"<br>3. 点击登录按钮 | 1. 成功跳转至系统首页 | P1 | 边界测试 | 需求2 |\n| LOGIN_003 | 验证最大长度用户名 | 1. 系统运行正常<br>2. 存在用户名为20字符的用户(abcdefghijklmnopqrst) | 1. 输入用户名\"abcdefghijklmnopqrst\"<br>2. 输入有效密码\"Abcd1234\"<br>3. 点击登录按钮 | 1. 成功跳转至系统首页 | P1 | 边界测试 | 需求2 |\n| LOGIN_004 | 验证最小长度密码 | 1. 系统运行正常<br>2. 存在测试用户(testuser123) | 1. 输入用户名\"testuser123\"<br>2. 输入8字符密码\"Ab1cdefg\"<br>3. 点击登录按钮 | 1. 成功跳转至系统首页 | P1 | 边界测试 | 需求3 |\n| LOGIN_005 | 验证无效用户名格式(5字符) | 1. 系统运行正常 | 1. 输入5字符用户名\"abcde\"<br>2. 输入有效密码\"Abcd1234\"<br>3. 点击登录按钮 | 1. 显示错误提示\"用户名长度需6-20个字符\"<br>2. 停留在登录页面 | P2 | 异常测试 | 需求2 |\n| LOGIN_006 | 验证纯数字密码 | 1. 系统运行正常<br>2. 存在测试用户(testuser123) | 1. 输入用户名\"testuser123\"<br>2. 输入纯数字密码\"12345678\"<br>3. 点击登录按钮 | 1. 显示错误提示\"密码必须包含字母和数字\"<br>2. 停留在登录页面 | P2 | 异常测试 | 需求3 |\n| LOGIN_007 | 验证空密码登录 | 1. 系统运行正常<br>2. 存在测试用户(testuser123) | 1. 输入用户名\"testuser123\"<br>2. 密码框留空<br>3. 点击登录按钮 | 1. 显示错误提示\"请输入密码\"<br>2. 停留在登录页面 | P1 | 异常测试 | 需求1 |", "generation_id": "20250629_162914", "status": "linked", "linked_timestamp": "2025-06-29 16:29:14"}