import re
from typing import List, Dict, Any

class ResultParser:
    """
    AI模型返回结果处理工具类
    """
    @staticmethod
    def extract_markdown_blocks(text: str) -> List[str]:
        """
        提取所有markdown代码块（```...```）内容
        """
        return re.findall(r'```(?:[a-zA-Z]*\n)?([\s\S]*?)```', text)

    @staticmethod
    def extract_tables_from_markdown(text: str) -> List[List[List[str]]]:
        """
        从markdown文本中提取所有表格，返回为二维数组列表
        """
        tables = []
        # 匹配markdown表格
        for table in re.findall(r'(\|.+\|\n)(\|[\s\S]+?\|\n)+', text):
            lines = table[0].split('\n')
            table_data = [
                [cell.strip() for cell in re.split(r'\s*\|\s*', line) if cell.strip()]
                for line in lines if line.strip()
            ]
            if table_data:
                tables.append(table_data)
        return tables

    @staticmethod
    def extract_plain_text(text: str) -> str:
        """
        提取去除markdown代码块后的纯文本
        """
        return re.sub(r'```[\s\S]*?```', '', text).strip()

    @staticmethod
    def extract_latest_markdown(text: str) -> str:
        """
        提取和处理markdown内容，支持以下格式：
        1. 表格形式的markdown
        2. 标题(#)和列表(-)形式的markdown
        3. 混合形式的markdown内容
        """
        blocks = ResultParser.extract_markdown_blocks(text)
        return blocks[-1] if blocks else text
    @staticmethod
    def extract_all(text: str) -> Dict[str, Any]:
        """
        综合提取：返回markdown块、表格、纯文本
        """
        return {
            'markdown_blocks': ResultParser.extract_markdown_blocks(text),
            'tables': ResultParser.extract_tables_from_markdown(text),
            'plain_text': ResultParser.extract_plain_text(text)
        }

    @staticmethod
    def extract_final_review_markdown(text: str) -> str:
        """
        从模型返回的消息体中，提取“评审测试用例最终结果”后面的markdown表格内容。
        优先提取 ```markdown ... ``` 代码块内容，否则尝试直接提取表格。
        """
        # 查找“评审测试用例最终结果”后的内容
        pattern = r"评审测试用例最终结果[\s\S]*?```markdown\n([\s\S]*?)```"
        match = re.search(pattern, text)
        if match:
            return match.group(1).strip()
        # 兼容没有markdown代码块的情况，直接找表格
        after_flag = text.split("评审测试用例最终结果", 1)[-1]
        table_match = re.search(r'(\|.+\|\n)(\|[\s\S]+?\|\n)+', after_flag)
        if table_match:
            return table_match.group(0).strip()
        return ""


