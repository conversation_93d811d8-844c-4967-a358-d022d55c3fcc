#!/usr/bin/env python3
"""
测试完整的认证流程
"""

import time
import requests
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_auth_flow():
    """测试认证流程"""
    print("=== 测试完整认证流程 ===")
    
    # 设置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument("--headless")  # 无头模式
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--disable-dev-shm-usage")
    
    driver = None
    try:
        # 检查应用是否运行
        print("1. 检查应用状态...")
        try:
            response = requests.get("http://localhost:8518", timeout=5)
            print(f"   应用状态: {response.status_code}")
        except Exception as e:
            print(f"   应用不可访问: {e}")
            return
        
        # 启动浏览器
        print("2. 启动浏览器...")
        driver = webdriver.Chrome(options=chrome_options)
        driver.get("http://localhost:8518")
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        
        print("3. 检查登录页面...")
        # 查找登录表单元素
        try:
            # 尝试找到用户名输入框
            username_input = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']"))
            )
            print("   ✅ 找到用户名输入框")
            
            # 尝试找到密码输入框
            password_input = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            print("   ✅ 找到密码输入框")
            
            # 尝试找到登录按钮
            login_button = driver.find_element(By.XPATH, "//button[contains(text(), '登录')]")
            print("   ✅ 找到登录按钮")
            
        except Exception as e:
            print(f"   ❌ 登录表单元素缺失: {e}")
            # 打印页面源码以便调试
            print("页面内容:")
            print(driver.page_source[:1000])
            return
        
        print("4. 执行登录...")
        # 输入用户名和密码
        username_input.clear()
        username_input.send_keys("admin")
        
        password_input.clear()
        password_input.send_keys("admin123")
        
        # 点击登录按钮
        login_button.click()
        
        # 等待登录完成
        time.sleep(3)
        
        print("5. 检查登录状态...")
        # 检查是否有登出按钮（表示登录成功）
        try:
            logout_button = wait.until(
                EC.presence_of_element_located((By.XPATH, "//button[contains(text(), '登出')]"))
            )
            print("   ✅ 登录成功，找到登出按钮")
        except Exception as e:
            print(f"   ❌ 登录可能失败: {e}")
            return
        
        print("6. 执行登出...")
        logout_button.click()
        
        # 等待登出完成
        time.sleep(3)
        
        print("7. 检查登出后状态...")
        # 检查是否重新显示登录表单
        try:
            username_input = wait.until(
                EC.presence_of_element_located((By.CSS_SELECTOR, "input[type='text']"))
            )
            password_input = driver.find_element(By.CSS_SELECTOR, "input[type='password']")
            login_button = driver.find_element(By.XPATH, "//button[contains(text(), '登录')]")
            
            print("   ✅ 登出成功，登录表单重新显示")
            
            # 测试输入框是否可用
            username_input.clear()
            username_input.send_keys("test")
            if username_input.get_attribute("value") == "test":
                print("   ✅ 用户名输入框可正常输入")
            else:
                print("   ❌ 用户名输入框无法输入")
            
            password_input.clear()
            password_input.send_keys("test")
            if password_input.get_attribute("value") == "test":
                print("   ✅ 密码输入框可正常输入")
            else:
                print("   ❌ 密码输入框无法输入")
                
        except Exception as e:
            print(f"   ❌ 登出后登录表单不可用: {e}")
            # 打印页面源码以便调试
            print("登出后页面内容:")
            print(driver.page_source[:1000])
        
        print("\n🎉 认证流程测试完成！")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    finally:
        if driver:
            driver.quit()

if __name__ == "__main__":
    test_auth_flow()
