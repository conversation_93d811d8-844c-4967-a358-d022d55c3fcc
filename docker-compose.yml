version: '3.8'

services:
  testcase-generator:
    build: .
    image: testcase-generator:v2025062801
    container_name: testcase-generator
    ports:
      - "8501:8501"
    volumes:
      - ./logs:/app/logs
      - ./settings:/app/settings
    environment:
      - STREAMLIT_SERVER_PORT=8501
      - STREAMLIT_SERVER_ADDRESS=0.0.0.0
      - LOG_LEVEL=INFO
      - LOG_DIR=/app/logs
      # API密钥通过环境变量传递
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY:-}
      - QWEN_API_KEY=${QWEN_API_KEY:-}
    env_file:
      - .env
    deploy:
      resources:
        limits:
          memory: 1G       # 增加内存限制到1GB
          cpus: '2.0'      # 增加CPU限制到2核
        reservations:
          memory: 256M     # 预留内存
          cpus: '0.5'      # 预留CPU
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8501/_stcore/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - testcase-network

networks:
  testcase-network:
    driver: bridge