"""常量管理模块"""

import configparser
from pathlib import Path

class ModelConstants:
    """模型相关常量管理类"""
    
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self._load_config()
            self._initialized = True
            
    def _load_config(self):
        """加载配置文件"""
        config = configparser.ConfigParser()
        config_path = Path(__file__).parent.parent / "settings" / "model_config.ini"
        config.read(config_path, encoding='utf-8')
        
        # 读取可用模型列表
        self.AVAILABLE_MODELS = [
            model.strip() 
            for model in config.get('models', 'available_models', fallback='').split(',')
            if model.strip()
        ]
        
        # 读取默认模型配置
        self.DEFAULT_GENERATOR = config.get('defaults', 'default_generator', fallback='deepseek')
        self.DEFAULT_REVIEWER = config.get('defaults', 'default_reviewer', fallback='qwen')
        
    def reload(self):
        """重新加载配置"""
        self._load_config()

# 创建全局实例
model_constants = ModelConstants()

__all__ = ['model_constants']
