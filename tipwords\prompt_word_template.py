# prompt_word_template.py
"""
统一管理测试用例相关 prompt word 路径
"""
import os
from utils.logger import setup_logger

# 设置日志（调用自定义logger）
logger = setup_logger()


# 获取当前目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
logger.info(f"当前目录提示词路径: {BASE_DIR}")

# 测试用例生成系统消息模板路径
TESTCASE_WRITER_SYSTEM_MESSAGE_PATH = os.path.abspath(
    os.path.join(BASE_DIR, 'test_case_prompt_words', 'TESTCASE_WRITER_SYSTEM_MESSAGE.txt')
)
logger.info(f"测试用例生成系统消息模板路径: {TESTCASE_WRITER_SYSTEM_MESSAGE_PATH}")

# 测试用例评审系统消息模板路径
TESTCASE_READER_SYSTEM_MESSAGE_PATH = os.path.abspath(
    os.path.join(BASE_DIR, 'review_case_prompt_words', 'TESTCASE_READER_SYSTEM_MESSAGE.txt')
)
logger.info(f"测试用例评审系统消息模板路径: {TESTCASE_READER_SYSTEM_MESSAGE_PATH}")
