"""测试用例生成菜单下，输入处理工具模块"""

import streamlit as st
from typing import Dict, Any, Tu<PERSON>,List
import configparser

# 高级参数设置测试用例配置文件路径
advanced_parameter_settings_test_cases = "settings/testcaseconfig.ini"

def get_user_input() -> str:
    """获取用户输入"""
    uploaded_file = st.file_uploader("上传需求文档", type=["txt","md"], label_visibility="collapsed", key="generation_file_uploader")
    uploaded_text = ""
    if uploaded_file is not None:
        uploaded_text = uploaded_file.read().decode('utf-8', 'ignore')

        # 记录文档上传日志（每次上传只记录一次）
        try:
            # 使用文件的file_id作为唯一标识
            file_id = uploaded_file.file_id if hasattr(uploaded_file, 'file_id') else f"{uploaded_file.name}_{uploaded_file.size}"

            # 检查是否已经记录过这次上传
            if st.session_state.get("last_logged_file_id") != file_id:
                from utils.upload_log_manager import upload_log_manager
                upload_id = upload_log_manager.record_upload(
                    uploaded_file=uploaded_file,
                    upload_type="generation",
                    content=uploaded_text
                )
                # 将upload_id保存到session_state中，用于后续关联
                st.session_state.current_upload_id = upload_id
                # 记录这次上传的文件ID
                st.session_state.last_logged_file_id = file_id
        except Exception as e:
            st.warning(f"记录上传日志失败: {str(e)}")

    return st.text_area(
        "需求描述",
        height=250,
        value=uploaded_text,
        placeholder="请详细描述你的功能需求，或上传需求文档自动填充。"
    )

def _parse_int_with_comment(val, default=0):
    if val is None:
        return default
    val = str(val).split(';')[0].strip()
    try:
        return int(val)
    except Exception:
        return default

def save_advanced_options(cases_rate_list, test_priority, test_case_count, review_count):
    config = configparser.ConfigParser()
    config.read(advanced_parameter_settings_test_cases, encoding="utf-8")
    if "advanced_options" not in config:
        config["advanced_options"] = {}
    section = config["advanced_options"]
    section["functional_testing"] = str(cases_rate_list[0])
    section["boundary_testing"] = str(cases_rate_list[1])
    section["exception_testing"] = str(cases_rate_list[2])
    section["perfmon_testing"] = str(cases_rate_list[3])
    section["regression_testing"] = str(cases_rate_list[4])
    section["test_priority"] = test_priority
    section["test_case_count"] = str(test_case_count)
    section["review_count"] = str(review_count)
    with open(advanced_parameter_settings_test_cases, "w", encoding="utf-8") as f:
        config.write(f)


def load_advanced_options():
    config = configparser.ConfigParser()
    config.read(advanced_parameter_settings_test_cases, encoding="utf-8")
    section = config["advanced_options"]
    return [
        _parse_int_with_comment(section.get("functional_testing", 55), 55),
        _parse_int_with_comment(section.get("boundary_testing", 25), 25),
        _parse_int_with_comment(section.get("exception_testing", 20), 20),
        _parse_int_with_comment(section.get("perfmon_testing", 0), 0),
        _parse_int_with_comment(section.get("regression_testing", 0), 0),
    ], section.get("test_priority", "--").split(';')[0].strip(), _parse_int_with_comment(section.get("test_case_count", 0), 0), _parse_int_with_comment(section.get("review_count", 1), 1)


def render_advanced_options() -> Tuple[List[int], str, int, int]:
    """
    渲染高级选项组件，带保存按钮

    Returns:
        Tuple[List[int], str, int, int]: (用例比例列表, 测试优先级, 用例数量, 评审次数)
    """
    cases_rate_list, test_priority, test_case_count, review_count = load_advanced_options()
    cases_rate_list_new = cases_rate_list.copy()
    with st.expander("高级选项"):
        show_slider = st.checkbox('用例分类占比(%)', True)
        cols = st.columns([2, 2])
        if show_slider:
            cases_rate_list_new[0] = cols[0].slider("功能用例", min_value=0, max_value=100, value=cases_rate_list[0])
            cases_rate_list_new[1] = cols[0].slider("边界用例", min_value=0, max_value=100, value=cases_rate_list[1])
            cases_rate_list_new[2] = cols[0].slider("异常用例", min_value=0, max_value=100, value=cases_rate_list[2])
            cases_rate_list_new[3] = cols[1].slider("性能/兼容性用例", min_value=0, max_value=100, value=cases_rate_list[3])
            cases_rate_list_new[4] = cols[1].slider("回归测试用例", min_value=0, max_value=100, value=cases_rate_list[4])
        test_priority_new = st.selectbox(
            "测试优先级", ["--", "急", "高", "中", "低"], index=["--", "急", "高", "中", "低"].index(test_priority) if test_priority in ["--", "急", "高", "中", "低"] else 0)
        test_case_count_new = st.number_input(
            "生成测试用例数量",
            min_value=0,
            max_value=100,
            value=test_case_count,
            step=1,
            help="指定需要生成的测试用例数量"
        )
        review_count_new = st.number_input(
            "评审次数",
            min_value=1,
            max_value=10,
            value=review_count,
            step=1,
            help="设置AI模型的评审次数"
        )
        if st.button("保存高级选项设置"):
            save_advanced_options(cases_rate_list_new, test_priority_new, test_case_count_new, review_count_new)
            st.success("高级选项设置已保存！")
    return cases_rate_list_new, test_priority_new, test_case_count_new, review_count_new




def get_test_parameters() -> dict:
    """获取测试参数"""
    cases_rate_list, test_priority, test_case_count, review_count = render_advanced_options()
    return {
        'rates': cases_rate_list,
        'priority': test_priority,
        'count': test_case_count,
        'review_count': review_count
    }
