"""
模型角色管理模块。
支持：
1. 单模型模式：仅生成用例/仅评审用例
2. 双模型模式：模型生成测试用例+模型评审
"""

from typing import List, Optional, Tuple, Dict
from enum import Enum
from dataclasses import dataclass
import streamlit as st
from utils.logger import setup_logger


# 设置日志
logger = setup_logger()


class RoleType(Enum):
    """角色类型枚举"""
    GENERATOR = "generator"
    REVIEWER = "reviewer"
    BOTH = "both"
    NONE = "none"

@dataclass
class RoleState:
    """角色状态"""
    role_type: RoleType
    generator: Optional[str]
    reviewer: Optional[str]
    
    def is_valid(self) -> bool:
        """验证状态是否有效"""
        if self.role_type == RoleType.NONE:
            return True
        if self.role_type == RoleType.GENERATOR:
            return bool(self.generator and not self.reviewer)
        if self.role_type == RoleType.REVIEWER:
            return bool(not self.generator and self.reviewer)
        if self.role_type == RoleType.BOTH:
            return bool(self.generator and self.reviewer)
        return False

@dataclass
class ModelRoles:
    """模型角色配置"""
    generator: str  # 生成测试用例的模型
    reviewer: str  # 评审测试用例的模型

class RoleManager:
    """模型角色管理器"""
    
    AVAILABLE_MODELS = ["deepseek", "qwen"]
    
    def __init__(self):
        """初始化角色管理器"""
        # 确保session_state中有role_state（应该已经在session_utils中初始化了）
        if not hasattr(st.session_state, 'role_state'):
            st.session_state.role_state = {
                'generator_model': None,
                'reviewer_model': None,
                'role_type': 'none'  # 初始状态设为none
            }
            logger.info("初始化role_state在session_state中")

        self._role_type = st.session_state.role_state.get('role_type', 'none')
        self.generator_model = st.session_state.role_state.get('generator_model')
        self.reviewer_model = st.session_state.role_state.get('reviewer_model')
        logger.debug(f"RoleManager初始化完成: role_type={self._role_type}, generator={self.generator_model}, reviewer={self.reviewer_model}")

    def reload_state(self) -> None:
        """从 session_state 中重新加载角色状态"""
        state = st.session_state.role_state if hasattr(st.session_state, 'role_state') else {}
        self.generator_model = state.get('generator_model')
        self.reviewer_model = state.get('reviewer_model')
        self._role_type = state.get('role_type', 'none')
        
    @property
    def role_type(self) -> str:
        """获取当前角色类型"""
        return self._role_type
        
    @role_type.setter
    def role_type(self, value: str) -> None:
        """设置当前角色类型"""
        self._role_type = value
        
    @property
    def state(self) -> RoleState:
        """获取当前状态"""
        self.reload_state()  # 确保状态最新
        return RoleState(
            role_type=RoleType(self.role_type),
            generator=self.generator_model,
            reviewer=self.reviewer_model
        )

    def set_single_role(self, model_name: str, role_type: str) -> None:
        """设置单一角色模型"""
        # 保存旧状态用于恢复
        old_state = st.session_state.role_state.copy()
        try:
            if role_type == "generator":
                self.generator_model = model_name
                self.reviewer_model = None
                self.role_type = "generator"
            else:  # reviewer
                self.generator_model = None
                self.reviewer_model = model_name
                self.role_type = "reviewer"
            self._save_state()
            logger.info(f"已设置为{role_type}模式，模型: {model_name}")
        except Exception as e:
            # 恢复旧状态
            st.session_state.role_state = old_state
            logger.error(f"设置角色失败: {str(e)}")
            raise

    def set_roles(self, generator: str, reviewer: Optional[str] = None) -> None:
        """设置双角色模型"""
        self.generator_model = generator
        self.reviewer_model = reviewer or generator
        self.role_type = "both"
        self._save_state()



    def _save_state(self) -> None:
        """保存当前状态到session_state和URL参数"""
        if not hasattr(st.session_state, 'role_state'):
            st.session_state.role_state = {}

        st.session_state.role_state.update({
            'generator_model': self.generator_model,
            'reviewer_model': self.reviewer_model,
            'role_type': self._role_type
        })

        # 同时保存到URL参数以支持页面刷新
        try:
            if self.generator_model:
                st.query_params.generator = self.generator_model
            if self.reviewer_model:
                st.query_params.reviewer = self.reviewer_model
            if self._role_type:
                st.query_params.role_type = self._role_type
        except Exception as e:
            logger.warning(f"保存状态到URL失败: {e}")

        logger.debug(f"已保存角色状态: generator={self.generator_model}, reviewer={self.reviewer_model}, type={self._role_type}")

    def validate_model(self, model_name: str) -> bool:
        """验证模型名称是否有效"""
        return model_name in self.AVAILABLE_MODELS
        
    def get_roles(self) -> ModelRoles:
        """获取当前的模型角色设置"""
        self.reload_state()  # 确保更新到最新状态
        if not self.generator_model and not self.reviewer_model:
            raise ValueError("未设置任何模型角色")
        return ModelRoles(
            generator=self.generator_model or "未设置",
            reviewer=self.reviewer_model or "未设置"
        )
    
    def get_role_info(self) -> Tuple[str, List[str]]:
        """获取角色配置信息"""
        self.reload_state()  # 确保更新到最新状态
        try:
            current_state = st.session_state.role_state
            generator = current_state['generator_model']
            reviewer = current_state['reviewer_model']
            role_type = current_state['role_type']

            if not generator and not reviewer:
                return "未配置", []
            if role_type == "generator":
                return "仅生成模式", [f"✓ {generator} 负责生成测试用例"]
            if role_type == "reviewer":
                return "仅评审模式", [f"✓ {reviewer} 负责评审测试用例"]
            if generator and reviewer and generator != reviewer:
                return "双模型模式", [f"✓ {generator} 负责生成测试用例", f"✓ {reviewer} 负责评审测试用例"]
            if generator:
                return "单模型模式", [f"✓ {generator} 同时负责生成和评审测试用例"]
            return "未知模式", []
        except Exception as e:
            logger.error(f"获取角色配置信息失败: {str(e)}")
            return "配置错误", [f"错误信息: {str(e)}"]    
    def is_single_model_mode(self) -> bool:
        """检查是否为单模型模式"""
        # 如果没有启用任何模型，强制为单模型模式
        enabled_models = self.get_enabled_models()
        if not enabled_models:
            return True
            
        # 如果只启用了一个模型，强制为单模型模式
        if len(enabled_models) == 1:
            return True
            
        # 如果是生成器或评审者角色，就是单模型模式
        if self.role_type in ["generator", "reviewer"]:
            return True
            
        # 如果使用同一个模型，也是单模型模式
        return (self.generator_model == self.reviewer_model) and self.generator_model is not None
    
    def get_enabled_models(self) -> List[str]:
        """获取当前启用的所有模型名称"""
        enabled = []
        if self.generator_model:
            enabled.append(self.generator_model)
        if self.reviewer_model and self.reviewer_model != self.generator_model:
            enabled.append(self.reviewer_model)
        return enabled
    
    def clear_roles(self) -> None:
        """清除所有角色设置"""
        self._state = RoleState(
            role_type=RoleType.NONE,
            generator=None,
            reviewer=None
        )
        logger.info("已清除所有模型角色设置")

    async def render_role_settings(self, enabled_models: list) -> None:
        """渲染角色设置界面"""
        st.markdown("### 🔄 模型角色设置")
        
        if not enabled_models:
            st.warning("请至少启用一个模型")
            return
            
        if len(enabled_models) > 2:
            st.warning("检测到超过2个启用的模型。系统将只使用前两个模型")
            enabled_models = enabled_models[:2]

        # 根据模型数量显示不同的场景选项
        if len(enabled_models) == 1:
            self._render_single_model_mode(enabled_models[0])
        else:
            self._render_dual_model_mode(enabled_models)

        # 显示当前配置
        self._render_current_config()

    def _render_single_model_mode(self, model_name: str) -> None:
        """渲染单模型模式界面"""
        # 从URL参数或当前状态确定默认选择
        query_params = st.query_params
        url_scenario = query_params.get("scenario", None)

        # 初始化或获取当前场景选择
        if 'single_model_scenario' not in st.session_state:
            if url_scenario:
                st.session_state.single_model_scenario = url_scenario
            else:
                st.session_state.single_model_scenario = (
                    "单模型-仅生成测试用例"
                    if self.role_type == "generator"
                    else "单模型-仅评审测试用例"
                )

        scenario_options = [
            "单模型-仅生成测试用例",
            "单模型-仅评审测试用例"
        ]

        # 确定当前选择的索引
        current_index = 0
        if st.session_state.single_model_scenario in scenario_options:
            current_index = scenario_options.index(st.session_state.single_model_scenario)

        scenario = st.radio(
            "选择使用场景",
            options=scenario_options,
            index=current_index,
            help="选择单个模型的使用场景",
            key="scenario_radio"
        )

        # 更新选择状态和URL参数
        if scenario != st.session_state.single_model_scenario:
            st.session_state.single_model_scenario = scenario
            st.query_params.scenario = scenario

        if scenario == "单模型-仅生成测试用例":
            self.set_single_role(model_name, "generator")
            st.info(f"✅ 已设置 {model_name} 仅用于生成测试用例")
        else:  # 单模型-仅评审测试用例
            self.set_single_role(model_name, "reviewer")
            st.info(f"✅ 已设置 {model_name} 仅用于评审测试用例")

    def _render_dual_model_mode(self, enabled_models: List[str]) -> None:
        """渲染双模型模式界面"""
        st.markdown("#### 双模型-模型生成测试用例+模型评审")
        col1, col2 = st.columns(2)
        
        with col1:
            generator = st.selectbox(
                "选择生成测试用例的模型",
                enabled_models,
                index=0
            )
        
        with col2:
            reviewer_options = [m for m in enabled_models if m != generator]
            reviewer = st.selectbox(
                "选择评审测试用例的模型",
                reviewer_options or enabled_models,
                index=0
            )
        
        self.set_roles(generator, reviewer)
        st.info(f"✅ 已设置生成模型为 {generator}，评审模型为 {reviewer}")

    def _render_current_config(self) -> None:
        """渲染当前配置信息"""
        try:
            mode, details = self.get_role_info()
            st.info("\n".join([mode] + details))
            
            if st.button("保存角色设置", key="save_role_settings"):
                self._save_state()
                st.success("✅ 角色设置已保存！")
                # 删除自动刷新，避免重置会话状态
                # st.rerun()
                
        except Exception as e:
            logger.error(f"渲染角色配置失败: {str(e)}")
            st.error(f"保存角色配置失败: {str(e)}")

# 创建全局角色管理器实例
role_manager = RoleManager()

# 导出
__all__ = ['RoleManager', 'ModelRoles', 'role_manager']
