"""
LLM模型管理包。
提供模型配置、实例管理和调用接口。
"""

from .model_config import (
    ModelConfig,
    ModelInfo,
    ModelParameters,
    DEFAULT_MODELS,
    DEEPSEEK_MODEL_INFO,
    QWEN_MODEL_INFO
)
from .model_manager import ModelManager

# 创建全局模型管理器实例
model_manager = ModelManager()

# 导出所有需要的类和实例
__all__ = [
    'ModelConfig',
    'ModelInfo',
    'ModelParameters',
    'ModelManager',
    'model_manager',
    'DEFAULT_MODELS',
    'DEEPSEEK_MODEL_INFO',
    'QWEN_MODEL_INFO',
]

# 版本信息
__version__ = '0.1.0'